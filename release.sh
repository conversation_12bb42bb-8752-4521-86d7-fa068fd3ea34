#!/bin/bash

# Release script for Electrobox
# Usage: ./release.sh <version>

if [ -z "$1" ]; then
    echo "Usage: ./release.sh <version>"
    echo "Example: ./release.sh 1.0.2"
    exit 1
fi

VERSION=$1

echo "🚀 Creating release for version $VERSION..."

# Build the app
echo "📦 Building the app..."
npm run dist

# Commit changes
echo "📝 Committing changes..."
git add .
git commit -m "Release v$VERSION"

# Create and push tag
echo "🏷️ Creating tag v$VERSION..."
git tag v$VERSION
git push origin main
git push origin v$VERSION

echo "✅ Release v$VERSION created!"
echo ""
echo "📋 Next steps:"
echo "1. Go to https://github.com/jerome232/electrobox/releases"
echo "2. Edit the v$VERSION release"
echo "3. Upload these files:"
echo "   - dist/electrobox-$VERSION.dmg"
echo "   - dist/electrobox-$VERSION-mac.zip"
echo "4. Publish the release"
echo ""
echo "🎉 Users will get auto-updates when they run the app!" 