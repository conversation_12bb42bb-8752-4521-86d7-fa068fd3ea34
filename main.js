const { app, BrowserWindow, ipcMain, globalShortcut } = require('electron');
const path = require('path');
const isDev = process.env.NODE_ENV === 'development';
console.log('🔍 Environment check:', {
  NODE_ENV: process.env.NODE_ENV,
  isDev: isDev,
  platform: process.platform
});

// Add electron-updater for auto-update
const { autoUpdater } = require('electron-updater');

// Import our modules
const ChatDatabase = require('./database');
const AIService = require('./ai-service');

let mainWindow;
let database;
let aiService;

function createWindow() {
  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js')
    },
    icon: path.join(__dirname, 'assets/Icon.png'), // Optional: add an icon
    titleBarStyle: 'default',
    show: false // Don't show until ready
  });

  // Load the app
  if (isDev) {
    // In development, load from React dev server
    console.log('🚀 Loading development URL: http://localhost:5173');
    mainWindow.loadURL('http://localhost:5173');
    // Open DevTools in development
    mainWindow.webContents.openDevTools();
  } else {
    // In production, load the built React app
    const prodPath = path.join(__dirname, 'renderer/dist/index.html');
    console.log('🚀 Loading production file:', prodPath);
    mainWindow.loadFile(prodPath);
  }

  // Show window when ready to prevent visual flash
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  // Handle window closed
  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

// Initialize services
function initializeServices() {
  database = new ChatDatabase();
  aiService = new AIService(null, database);
  
  // Load saved API key from new structure
  const activeApiKey = database.getActiveApiKey();
  if (activeApiKey) {
    aiService.setApiKey(activeApiKey.api_key);
  }
}

// This method will be called when Electron has finished initialization
app.whenReady().then(() => {
  initializeServices();
  createWindow();
  
  // Register keyboard shortcuts for DevTools
  if (isDev) {
    // F12 to toggle DevTools for renderer process
    globalShortcut.register('F12', () => {
      if (mainWindow) {
        mainWindow.webContents.toggleDevTools();
      }
    });
    
    // Cmd+Shift+I (Mac) or Ctrl+Shift+I (Windows/Linux) to toggle DevTools
    globalShortcut.register('CommandOrControl+Shift+I', () => {
      if (mainWindow) {
        mainWindow.webContents.toggleDevTools();
      }
    });
  }
  
  // Check for updates in production
  if (!isDev) {
    autoUpdater.checkForUpdatesAndNotify();
  }
});

// Quit when all windows are closed
app.on('window-all-closed', () => {
  // On macOS it is common for applications and their menu bar
  // to stay active until the user quits explicitly with Cmd + Q
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  // On macOS it's common to re-create a window in the app when the
  // dock icon is clicked and there are no other windows open
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// Clean up on app quit
app.on('before-quit', () => {
  if (database) {
    database.close();
  }
});

// IPC handlers for database operations
ipcMain.handle('save-message', async (event, message) => {
  try {
    const id = database.saveMessage(message.content, message.sender, message.conversationId);
    return { success: true, id };
  } catch (error) {
    console.error('Error saving message:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('get-messages', async (event, conversationId = 'default') => {
  try {
    const messages = database.getMessages(conversationId);
    return { success: true, messages };
  } catch (error) {
    console.error('Error getting messages:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('clear-messages', async (event, conversationId = 'default') => {
  try {
    database.clearMessages(conversationId);
    return { success: true };
  } catch (error) {
    console.error('Error clearing messages:', error);
    return { success: false, error: error.message };
  }
});

// IPC handlers for AI operations
ipcMain.handle('send-to-ai', async (event, data) => {
  try {
    let response;
    if (typeof data === 'string') {
      // Backward compatibility
      response = await aiService.sendMessage(data);
    } else {
      // New format with prompt type
      const { message, promptType } = data;
      response = await aiService.sendMessage(message, 'deepseek/deepseek-chat-v3-0324', null, null, promptType);
    }
    return { success: true, response };
  } catch (error) {
    console.error('Error sending to AI:', error);
    return { success: false, error: error.message };
  }
});

// IPC handlers for settings
ipcMain.handle('save-api-key', async (event, apiKey, name = 'Default', setActive = true) => {
  try {
    const id = database.saveApiKey(name, apiKey, setActive);
    if (setActive) {
      aiService.setApiKey(apiKey);
    }
    return { success: true, id };
  } catch (error) {
    console.error('Error saving API key:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('get-api-keys', async (event) => {
  try {
    const apiKeys = database.getApiKeys();
    return { success: true, apiKeys };
  } catch (error) {
    console.error('Error getting API keys:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('get-active-api-key', async (event) => {
  try {
    const activeApiKey = database.getActiveApiKey();
    if (activeApiKey) {
      return { success: true, apiKey: activeApiKey };
    } else {
      return { success: false, error: 'No active API key found' };
    }
  } catch (error) {
    console.error('Error getting active API key:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('set-active-api-key', async (event, id) => {
  try {
    database.setActiveApiKey(id);
    const activeApiKey = database.getActiveApiKey();
    if (activeApiKey) {
      aiService.setApiKey(activeApiKey.api_key);
    }
    return { success: true };
  } catch (error) {
    console.error('Error setting active API key:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('delete-api-key', async (event, id) => {
  try {
    database.deleteApiKey(id);
    return { success: true };
  } catch (error) {
    console.error('Error deleting API key:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('update-api-key-name', async (event, id, name) => {
  try {
    database.updateApiKeyName(id, name);
    return { success: true };
  } catch (error) {
    console.error('Error updating API key name:', error);
    return { success: false, error: error.message };
  }
});

// IPC handlers for system prompt settings
ipcMain.handle('get-system-prompt-settings', async (event) => {
  try {
    const settings = database.getSystemPromptSettings();
    return { success: true, settings };
  } catch (error) {
    console.error('Error getting system prompt settings:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('get-default-system-prompt', async (event) => {
  try {
    const prompt = database.getDefaultSystemPrompt();
    return { success: true, prompt };
  } catch (error) {
    console.error('Error getting default system prompt:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('set-default-system-prompt', async (event, id) => {
  try {
    database.setDefaultSystemPrompt(id);
    return { success: true };
  } catch (error) {
    console.error('Error setting default system prompt:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('save-system-prompt-setting', async (event, name, description, prompts, isDefault = false) => {
  try {
    const id = database.saveSystemPromptSetting(name, description, prompts, isDefault);
    return { success: true, id };
  } catch (error) {
    console.error('Error saving system prompt setting:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('update-system-prompt-setting', async (event, id, name, description, prompts) => {
  try {
    database.updateSystemPromptSetting(id, name, description, prompts);
    return { success: true };
  } catch (error) {
    console.error('Error updating system prompt setting:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('delete-system-prompt-setting', async (event, id) => {
  try {
    database.deleteSystemPromptSetting(id);
    return { success: true };
  } catch (error) {
    console.error('Error deleting system prompt setting:', error);
    return { success: false, error: error.message };
  }
});

// Legacy handler for backward compatibility
ipcMain.handle('get-api-key', async (event) => {
  try {
    const activeApiKey = database.getActiveApiKey();
    return { success: true, apiKey: activeApiKey ? activeApiKey.api_key : null };
  } catch (error) {
    console.error('Error getting API key:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('test-api-key', async (event, apiKey) => {
  try {
    // Temporarily set the API key for testing
    const originalApiKey = aiService.apiKey;
    aiService.setApiKey(apiKey);
    
    const result = await aiService.testConnection();
    
    // Restore original API key if test fails
    if (!result.success) {
      aiService.setApiKey(originalApiKey);
    }
    
    return result;
  } catch (error) {
    console.error('Error testing API key:', error);
    return { success: false, error: error.message };
  }
});

// IPC handlers for character settings
ipcMain.handle('save-character-setting', async (event, characterSetting) => {
  try {
    const id = database.saveCharacterSetting(characterSetting);
    return { success: true, id };
  } catch (error) {
    console.error('Error saving character setting:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('get-character-settings', async (event) => {
  try {
    const settings = database.getCharacterSettings();
    return { success: true, settings };
  } catch (error) {
    console.error('Error getting character settings:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('get-character-setting', async (event, id) => {
  try {
    const setting = database.getCharacterSetting(id);
    return { success: true, setting };
  } catch (error) {
    console.error('Error getting character setting:', error);
    return { success: false, error: error.message };
  }
});

// IPC handlers for world settings
ipcMain.handle('save-world-setting', async (event, worldSetting) => {
  try {
    const id = database.saveWorldSetting(worldSetting);
    return { success: true, id };
  } catch (error) {
    console.error('Error saving world setting:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('get-world-settings', async (event) => {
  try {
    const settings = database.getWorldSettings();
    return { success: true, settings };
  } catch (error) {
    console.error('Error getting world settings:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('get-world-setting', async (event, id) => {
  try {
    const setting = database.getWorldSetting(id);
    return { success: true, setting };
  } catch (error) {
    console.error('Error getting world setting:', error);
    return { success: false, error: error.message };
  }
});

// IPC handlers for chats
ipcMain.handle('save-chat', async (event, parentId, settingId, settingType, contents, userPersonaId = null, characterId = null) => {
  console.log('💾 IPC: save-chat called with:', {
    parentId,
    settingId,
    settingType,
    userPersonaId,
    characterId,
    contentsLength: contents ? contents.length : 0,
    contents: contents
  });
  try {
    const id = database.saveChat(parentId, settingId, settingType, contents, userPersonaId, characterId);
    console.log('💾 IPC: save-chat successful, returned ID:', id);
    return { success: true, id };
  } catch (error) {
    console.error('❌ Error saving chat:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('update-chat', async (event, chatId, contents) => {
  console.log('💾 IPC: update-chat called with:', {
    chatId,
    contentsLength: contents ? contents.length : 0,
    contents: contents
  });
  try {
    const success = database.updateChat(chatId, contents);
    console.log('💾 IPC: update-chat successful:', success);
    return { success: true, updated: success };
  } catch (error) {
    console.error('❌ Error updating chat:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('get-chats', async (event, settingId, settingType) => {
  console.log('📡 IPC: get-chats called with settingId:', settingId, 'settingType:', settingType);
  try {
    const chats = database.getChats(settingId, settingType);
    console.log('📡 Database returned chats:', chats);
    return { success: true, chats };
  } catch (error) {
    console.error('❌ Error getting chats:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('get-recent-chats', async (event, limit = 10) => {
  try {
    const chats = database.getRecentChats(limit);
    return { success: true, chats };
  } catch (error) {
    console.error('Error getting recent chats:', error);
    return { success: false, error: error.message };
  }
});

// New: list all chats with joined persona/character/world info (paginated)
ipcMain.handle('get-all-chats', async (event, limit = 50, offset = 0) => {
  try {
    const chats = database.getAllChats(limit, offset);
    return { success: true, chats };
  } catch (error) {
    console.error('Error getting all chats:', error);
    return { success: false, error: error.message };
  }
});

// Get directory chats - only root conversations for directory display
ipcMain.handle('get-directory-chats', async (event, limit = 20) => {
  try {
    const chats = database.getDirectoryChats(limit);
    return { success: true, chats };
  } catch (error) {
    console.error('Error getting directory chats:', error);
    return { success: false, error: error.message };
  }
});

// Get full conversation tree for resuming chats
ipcMain.handle('get-conversation-tree', async (event, rootChatId) => {
  try {
    const conversationData = database.getConversationTree(rootChatId);
    if (!conversationData) {
      return { success: false, error: 'Conversation not found' };
    }
    return { success: true, conversationData };
  } catch (error) {
    console.error('Error getting conversation tree:', error);
    return { success: false, error: error.message };
  }
});

// IPC handler for app info
ipcMain.handle('get-app-version', async (event) => {
  return app.getVersion();
});

// IPC handlers for persona creation
ipcMain.handle('save-persona', async (event, persona) => {
  try {
    const id = database.savePersona(persona);
    return { success: true, id };
  } catch (error) {
    console.error('Error saving persona:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('get-personas', async (event) => {
  try {
    const personas = database.getPersonas();
    return { success: true, personas };
  } catch (error) {
    console.error('Error getting personas:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('get-persona', async (event, id) => {
  try {
    const persona = database.getPersona(id);
    return { success: true, persona };
  } catch (error) {
    console.error('Error getting persona:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('delete-persona', async (event, id) => {
  try {
    database.deletePersona(id);
    return { success: true };
  } catch (error) {
    console.error('Error deleting persona:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('enhance-character-text', async (event, data) => {
  try {
    const enhancedText = await aiService.enhanceCharacterText(data.text, data.field, data.characterName);
    return { success: true, enhancedText };
  } catch (error) {
    console.error('Error enhancing character text:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('enhance-character-text-stream', async (event, data) => {
  console.log('🎯 IPC: enhance-character-text-stream called');
  console.log('  Field:', data.field);
  console.log('  Character:', data.characterName);
  console.log('  Text length:', data.text.length);
  
  try {
    let fullResponse = '';
    
    await aiService.enhanceCharacterText(
      data.text, 
      data.field, 
      data.characterName,
      (chunk) => {
        fullResponse += chunk;
        // Send chunk to renderer
        event.sender.send('enhance-character-text-chunk', { chunk, field: data.field });
      }
    );
    
    console.log('✅ IPC: enhance-character-text-stream completed successfully');
    return { success: true, enhancedText: fullResponse };
  } catch (error) {
    console.error('❌ IPC: Error enhancing character text:', error);
    return { success: false, error: error.message };
  }
});

// Database viewer IPC handlers
ipcMain.handle('get-all-tables', async (event) => {
  try {
    const tables = database.getAllTables();
    return { success: true, tables };
  } catch (error) {
    console.error('Error getting tables:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('get-table-schema', async (event, tableName) => {
  try {
    const schema = database.getTableSchema(tableName);
    return { success: true, schema };
  } catch (error) {
    console.error('Error getting table schema:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('get-table-data', async (event, tableName, limit = 100, offset = 0) => {
  try {
    const data = database.getTableData(tableName, limit, offset);
    const count = database.getTableCount(tableName);
    return { success: true, data, count };
  } catch (error) {
    console.error('Error getting table data:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('delete-row', async (event, tableName, rowId, idColumn = 'id') => {
  try {
    const deleted = database.deleteRow(tableName, rowId, idColumn);
    return { success: true, deleted };
  } catch (error) {
    console.error('Error deleting row:', error);
    return { success: false, error: error.message };
  }
});
