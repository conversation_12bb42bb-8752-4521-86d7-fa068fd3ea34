# Timeline Rendering Specification

This document defines robust, scalable connection logic between nodes for the timeline renderer.

Proposed timeline rendering instructions [docs.spec()](renderer/README.md:1)

1) Stable node identity
- Every node must have a globally unique, stable ID string generated from immutable domain fields (e.g., dataset primary key or a UUID persisted with the node). Never derive IDs from array indices or transient order.
- All data structures, DOM elements, and connection references must use this ID as the single source of truth. Example maps:
  - nodesById: Map<NodeId, Node>
  - anchorsById: Map<NodeId, AnchorSet>
  - edgesById: Map<EdgeId, { sourceId, sourceAnchor, targetId, targetAnchor }>
- Persist ID stability across rerenders, virtualization, and pagination.

2) Anchor-based connection geometry
- Compute connection endpoints from explicit anchor points on each node, not from DOM flow or bounding-box guesses.
- For each node define deterministic anchors in local node coordinates:
  - center, top, right, bottom, left
  - optional named ports: in-0..n, out-0..n
- Convert anchors to layout/world coordinates using the layout engine’s transform (including scroll, zoom, pan, and any canvas/SVG transforms). Do not rely on offsetTop/Left except as input to measured node size; prefer ResizeObserver and getBoundingClientRect once, then transform with known matrices.
- Maintain a transform pipeline: data-space -> layout-space -> world-space -> screen-space. All edge routing must operate in one consistent space (recommend: layout/world space).

3) Reactive layout invalidation and debounced updates
- Recalculate node positions and connection paths on any of:
  - data mutation (add/remove/update nodes or edges)
  - viewport resize
  - zoom/pan changes
  - node reflow (content or size change via fonts, images, async content)
- Use ResizeObserver per node to detect intrinsic size changes; batch with requestAnimationFrame or a 0–16ms microtask queue.
- Debounce layout recomputation with trailing-edge RAF and a max-wait (e.g., 50–100ms) for bursts. Use change tokens to coalesce updates; drop obsolete computations.

4) Deterministic, non-overlapping layout algorithm
- Use a deterministic layout that guarantees non-overlapping nodes and stable anchor placement under identical inputs:
  - Time-scaled hierarchical layout: X = timeScale(timestamp) with minimum lane spacing; Y determined by lane packing with conflict resolution.
  - Layered DAG (Sugiyama-style) for acyclic dependencies: long-edge splitting, crossing minimization (median/barycenter), brandes–köpf vertical alignment.
  - Force-directed with constraints: collisions enabled, fixed x by time, y-relaxation within lanes, capped iterations to achieve deterministic convergence via seeded RNG.
- Enforce minimum node size, horizontal/vertical padding, lane spacing, and inter-layer spacing. Record final anchors per node after resolving collisions.

5) Enforced render lifecycle
- Strict lifecycle per frame:
  1. data ingest/normalize -> IDs, nodes, edges, metadata
  2. layout compute -> node positions, sizes, anchors finalized
  3. node render -> paint nodes at computed positions
  4. connection path compute -> route edges using finalized anchor coordinates
  5. final paint -> draw edges in dedicated layer with z-ordering
- Edges must not compute until all node positions for the frame are frozen. Use a barrier or promise gating between steps 3 and 4.

6) Edge routing to minimize overlaps and crossings
- Provide selectable routing strategies:
  - Orthogonal routing with obstacle avoidance (grid-based A* or rectilinear manhattan router), minimum bend radius, snap to grid.
  - Spline routing (Catmull-Rom or cubic Bezier) with via-points from layered routing; avoid obstacles by detouring around node bounding boxes + padding.
  - Layered routing: assign edge layers per hierarchy to reduce crossings; apply median heuristic for ordering inside each layer.
- Always inflate obstacles by edge thickness + hover halo to prevent visual overlap.
- Limit bend count; prefer fewer segments while respecting avoidance.

7) Pixel alignment and subpixel accuracy
- Keep anchor coordinates in float world-space, but snap final stroke endpoints to device pixels to prevent blurry antialiasing:
  - If using canvas: align strokes to 0.5 px offsets for odd-width lines.
  - If using SVG: set shape-rendering="geometricPrecision"; round endpoints to nearest 0.5 when rendering straight segments; preserve continuous curves without jitter by snapping only at endpoints and orthogonal bends.
- Avoid cumulative transforms that create subpixel gaps between node boundary and edge endpoints; compute in the same coordinate space as node placement.

8) Layered rendering and z-index policy
- Render edges in a dedicated SVG or canvas layer beneath nodes:
  - nodes layer: z = 10
  - edges layer: z = 0
  - interaction overlay (selection, hover halos): z = 20
- Lines must not occlude node content unless explicitly elevated (e.g., selected edge), in which case temporarily raise in the overlay without shifting base layers.

9) Hit-testing and diagnostics
- Implement hit-testing per edge and anchor:
  - For SVG: use invisible thick stroke or separate hit path for pointer-events.
  - For canvas: cache path geometry for isPointInStroke tests.
- On every update, validate that each edge’s sourceId/targetId resolve to existing nodes and that named anchors exist; if invalid:
  - log structured diagnostics with edgeId, missing reference
  - visually flag the edge placeholder (dashed red to null island) or hide and add to an error panel
- Provide a developer debug mode toggling anchor/port markers and bounding boxes.

10) Stress testing and performance budgets
- Create automated scenarios:
  - large counts: 10k nodes, 20k edges
  - clustered densities: bursts of 1k nodes within a small time window
  - rapid updates: 60Hz data mutations and pan/zoom interactions
- Measure:
  - frame time (RAF budget <= 16ms target; edge routing sub-budget <= 6ms)
  - memory usage for geometry caches and spatial indices
  - layout iterations and cache hit ratios
- Optimize via:
  - spatial indexing (R-Tree/Quadtree) for obstacle queries
  - tile-based dirty region recompute; route only affected edges
  - caching anchor transforms; memoize repeated routes with stable keys
  - web workers for heavy layout/routing; main thread only paints

11) Fallbacks and async safety
- If layout fails or exceeds timeout:
  - hide connections for the frame, render nodes, schedule retry with exponential backoff
  - surface a non-blocking banner in dev builds with failure reason
- Guard against race conditions:
  - version frames with an incrementing epoch; discard late async results that don’t match the latest epoch
  - cancel in-flight worker tasks on new invalidations
  - freeze a frame’s node metrics snapshot for consistent edge routing

12) Documentation of assumptions and tunables
- Explicitly document:
  - time scale: domain range, pixel per unit, zoom limits
  - node default dimensions, min/max, content padding
  - lane spacing, inter-layer spacing, obstacle inflation
  - routing rules: preferred strategy, bend constraints, grid size
  - update triggers: which events invalidate layout vs. routes only
  - performance budgets and fallbacks
- Expose configuration with sane defaults and per-view overrides.

---

Reference architecture [typescript.interface()](renderer/src/components/timeline/types.ts:1)
- Types
  - NodeId = string
  - EdgeId = string
  - AnchorName = 'center' | 'top' | 'right' | 'bottom' | 'left' | `in-${number}` | `out-${number}`
  - TimelineNode { id: NodeId; data; size: {w,h}; anchors?: Record<AnchorName, Vec2Local> }
  - TimelineEdge { id: EdgeId; sourceId: NodeId; sourceAnchor: AnchorName; targetId: NodeId; targetAnchor: AnchorName }
  - LayoutResult { nodes: Record<NodeId, { x, y, w, h; anchorsWorld: Record<AnchorName, Vec2> }>; epoch: number }
- Events
  - invalidate(type: 'data'|'resize'|'zoom'|'reflow', ids?: string[])
- Public API
  - layout(nodes, edges, config) -> Promise<LayoutResult>
  - route(edges, layout, config) -> EdgePath[]
  - render(layout, paths)
  - debug.setEnabled(bool)

Render lifecycle pseudo-code [typescript.function()](renderer/src/components/timeline/engine.ts:1)
- High-level loop
  - state.epoch++
  - const current = state.epoch
  - await scheduleRAFDebounced()
  - const layout = await computeLayout(nodes, config, current)
  - if (layout.epoch !== state.epoch) return // stale
  - paintNodes(layout)
  - const paths = await routeEdges(edges, layout, config, current)
  - if (current !== state.epoch) return // stale
  - paintEdges(paths)

Routing choices [typescript.enum()](renderer/src/components/timeline/routing.ts:1)
- enum RouterKind { Orthogonal, Spline, Layered }
- config.router = RouterKind.Orthogonal
- Options: obstaclePadding, grid, bendPenalty, crossingPenalty, maxBends, strokeWidth, hitWidth

Performance hooks [typescript.function()](renderer/src/components/timeline/perf.ts:1)
- startMeasure('layout'|'route'|'paint')
- endMeasure()
- counters: nodesRouted, edgesRouted, cacheHits
- expose metrics to a dev panel

Diagnostics [typescript.function()](renderer/src/components/timeline/diagnostics.ts:1)
- validateReferences(nodesMap, edges) -> { ok: boolean, errors: ValidationError[] }
- drawDebugAnchors(layout)
- logErrors(errors)

Implementation notes [markdown.section()](renderer/README.md:120)
- Use SVG for edges for crisp scaling and CSS styling; Canvas optional for >50k edges with batching.
- Prefer a single root transform for zoom/pan; compute geometry in world space, apply a single CSS transform to layers.
- For ResizeObserver loops, batch reads before writes; avoid layout thrash.
- Determinism: seed RNG, stable sort keys by ID; never depend on object iteration order.

Acceptance criteria [markdown.list()](renderer/README.md:220)
- Nodes keep stable IDs and anchors across rerenders
- Edges compute from anchors with correct transforms under zoom/pan
- Layout re-runs on all defined triggers with debounced updates
- Deterministic placement and non-overlap guaranteed by chosen algorithm
- Edge routing avoids nodes and reduces crossings within budgets
- No visual gaps; endpoints snap precisely
- Z-index layering prevents lines over node content
- Hit-testing and diagnostics flag bad edges
- Stress tests run and record metrics; frame time within target
- Fallbacks hide edges on failure and recover without flicker
- Documentation covers all assumptions and tunables
