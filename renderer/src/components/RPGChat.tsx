import React, { useState, useRef, useEffect, useLayoutEffect } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { ChatArea } from './chat/ChatArea';
import { CharacterSheet } from './character/CharacterSheet';
import { ChoiceButtons } from './chat/ChoiceButtons';
import { SceneBackground } from './scene/SceneBackground';
import { SaveLoadPanel } from './save/SaveLoadPanel';
import { Button } from './ui/button';
import { Save, Menu, Settings, ArrowLeft, Sparkles } from 'lucide-react';
import { ChatParameters } from './ChatSetup';
import { PersonaImage } from './ui/persona-image';

export interface Character {
  id: string;
  name: string;
  avatar: string;
  stats: {
    strength: number;
    charm: number;
    arcana: number;
    luck: number;
  };
  inventory: InventoryItem[];
  relationships: Relationship[];
  level: number;
  experience: number;
}

export interface InventoryItem {
  id: string;
  name: string;
  type: 'weapon' | 'armor' | 'potion' | 'artifact' | 'misc';
  rarity: 'common' | 'uncommon' | 'rare' | 'legendary';
  description: string;
  effects?: string[];
}

export interface Relationship {
  characterName: string;
  level: number; // -100 to 100
  status: string; // "Hostile", "Neutral", "Friendly", "Allied"
}

export interface ChatMessage {
  id: string;
  sender: 'user' | 'ai';
  content: string;
  timestamp: number;
  characterName?: string;
  emotion?: 'neutral' | 'happy' | 'angry' | 'sad' | 'mysterious';
  choices?: DialogueChoice[];
}

export interface DialogueChoice {
  id: string;
  text: string;
  preview?: string;
  requirements?: {
    stat?: keyof Character['stats'];
    minValue?: number;
    hasItem?: string;
  };
  consequence?: string;
}

export interface GameState {
  currentScene: string;
  sceneBackground: string;
  mood: 'light' | 'neutral' | 'dark' | 'mysterious' | 'combat';
  chapter: string;
  questLog: string[];
}

// Timeline node type
interface TimelineNode {
  id: string;
  message: string;
  sender: 'user' | 'ai';
  choices?: DialogueChoice[];
  userInput?: string;
  parent?: TimelineNode | null;
  children: TimelineNode[];
  choiceId?: string; // The id of the choice that led to this node
  ref?: React.RefObject<HTMLDivElement>; // Added for measuring position
  chatId?: number; // Database chat ID for this node
  parentChatId?: number; // Parent chat ID for reconstruction
}

// Helper function to get character emoji based on class
const getCharacterEmoji = (characterClass: string): string => {
  const emojiMap: { [key: string]: string } = {
    adventurer: '🗡️',
    wizard: '🧙‍♂️',
    warrior: '⚔️',
    rogue: '🥷',
    cleric: '⛪',
    ranger: '🏹',
    bard: '🎵',
    monk: '🧘'
  };
  return emojiMap[characterClass] || '🗡️';
};

// Helper function to get scene background based on world setting
const getSceneBackground = (worldSetting: string): string => {
  const backgroundMap: { [key: string]: string } = {
    fantasy: 'forest',
    'sci-fi': 'space',
    'post-apocalyptic': 'wasteland',
    medieval: 'castle',
    cyberpunk: 'city',
    steampunk: 'factory',
    western: 'desert',
    modern: 'urban'
  };
  return backgroundMap[worldSetting] || 'forest';
};

// Helper function to create initial timeline based on parameters
const createInitialTimeline = (parameters?: ChatParameters): TimelineNode => {
  console.log('🎬 createInitialTimeline called with parameters:', parameters);

  if (!parameters) {
    console.log('🔄 No parameters provided, using default timeline');
    return {
      id: 'root',
      message: "Welcome to your adventure! What would you like to do?",
      sender: 'ai',
      choices: [
        { id: 'start', text: 'Begin my journey', preview: 'start the adventure' }
      ],
      children: [],
      parent: null
    };
  }

  let initialMessage = "Welcome to your adventure! What would you like to do?";
  let choices: DialogueChoice[] = [
    { id: 'start', text: 'Begin my journey', preview: 'start the adventure' }
  ];

  // 1) Prefer initialBootstrap passed directly via parameters to avoid reliance on globals
  if (parameters.initialBootstrap && Array.isArray(parameters.initialBootstrap.options)) {
    console.log('🧩 Using parameters.initialBootstrap for initial content/options');
    if (parameters.initialBootstrap.content && typeof parameters.initialBootstrap.content === 'string') {
      initialMessage = parameters.initialBootstrap.content;
    }
    const opts = parameters.initialBootstrap.options.slice(0, 4);
    if (opts.length > 0) {
      choices = opts.map((opt: string, index: number) => ({
        id: `initial-${Date.now()}-${index}`,
        text: String(opt),
        preview: String(opt).toLowerCase()
      }));
    }
  } else {
    // 2) Backward-compat: attempt to read window bootstrap set by preload
    try {
      const settingId =
        parameters.settingType === 'character'
          ? parameters.characterSetting?.id
          : parameters.worldSetting?.id;

      if (settingId) {
        const key = `${parameters.settingType}:${settingId}`;
        const bootstrapRoot = (window as any).__ELECTROBOX_BOOTSTRAP__;
        const bootstrap = bootstrapRoot?.initialChatBySetting?.[key];
        if (bootstrap) {
          console.log('🧩 Using bootstrap initial chat content/options:', bootstrap);
          if (bootstrap.content && typeof bootstrap.content === 'string') {
            initialMessage = bootstrap.content;
          }
          if (Array.isArray(bootstrap.options) && bootstrap.options.length > 0) {
            choices = bootstrap.options.slice(0, 4).map((opt: string, index: number) => ({
              id: `initial-${Date.now()}-${index}`,
              text: String(opt),
              preview: String(opt).toLowerCase()
            }));
          }
          // Clear bootstrap after first consumption to avoid stale carry-over between new chats
          try {
            if (bootstrapRoot?.initialChatBySetting) {
              delete bootstrapRoot.initialChatBySetting[key];
            }
          } catch {}
        }
      }
    } catch (e) {
      console.warn('Failed to read bootstrap initial options, will compute defaults:', e);
    }
  }

  // 3) If we still don't have 4 options, compute contextual defaults
  if (choices.length === 1) {
    if (parameters.settingType === 'character' && parameters.characterSetting) {
      console.log('👤 Creating timeline for character setting:', parameters.characterSetting);
      initialMessage = parameters.characterSetting.initial_message || initialMessage;
      choices = generateCharacterChoices(parameters.characterSetting);
      console.log('🎯 Generated character choices:', choices);
    } else if (parameters.settingType === 'world' && parameters.worldSetting) {
      console.log('🌍 Creating timeline for world setting:', parameters.worldSetting);
      initialMessage = `Welcome to ${parameters.worldSetting.name}. ${parameters.worldSetting.worldDescription}`;
      choices = generateWorldChoices(parameters.worldSetting);
      console.log('🎯 Generated world choices:', choices);
    }
  }

  const timeline: TimelineNode = {
    id: 'root',
    message: initialMessage,
    sender: 'ai' as const,
    choices: choices,
    children: [],
    parent: null
  };

  console.log('✅ Created initial timeline:', timeline);
  return timeline;
};

// Helper: generate contextual choices for character-based chats
const generateCharacterChoices = (characterSetting: any) => {
  // These should be AI-generated based on the character's personality and scenario
  // For now, using contextual defaults that will be replaced by AI generation
  return [
    { id: 'personality', text: 'Act according to your nature', preview: 'respond based on your personality' },
    { id: 'scenario', text: 'Engage with the scenario', preview: 'interact with your current situation' },
    { id: 'explore', text: 'Explore your surroundings', preview: 'investigate the environment' },
    { id: 'reflect', text: 'Reflect on your situation', preview: 'think about your circumstances' }
  ];
};

// Helper: generate contextual choices for world-based chats
const generateWorldChoices = (worldSetting: any) => {
  // These should be AI-generated based on the world description and character role
  // For now, using contextual defaults that will be replaced by AI generation
  return [
    { id: 'role', text: 'Embrace your role', preview: 'act according to your position in this world' },
    { id: 'world', text: 'Explore the world', preview: 'discover more about your environment' },
    { id: 'interact', text: 'Seek interaction', preview: 'look for others to engage with' },
    { id: 'adapt', text: 'Adapt to the situation', preview: 'adjust to your circumstances' }
  ];
};

// Helper: collect all nodes in the tree for navigation
function collectTimelineNodes(root: TimelineNode): TimelineNode[] {
  const nodes: TimelineNode[] = [];
  function traverse(node: TimelineNode) {
    nodes.push(node);
    node.children.forEach(traverse);
  }
  traverse(root);
  return nodes;
}

// Helper: get path from root to a node
function getTimelinePathToNode(node: TimelineNode): TimelineNode[] {
  const path: TimelineNode[] = [];
  let n: TimelineNode | undefined = node;
  while (n) {
    path.unshift(n);
    n = n.parent!;
  }
  return path;
}

// Helper: recursively collect node positions for line drawing (relative to container)
function collectNodePositions(
  node: TimelineNode,
  positions: {
    [key: string]: {
      x: number; y: number; width: number; height: number;
      anchorsWorld: { center: {x:number;y:number}; left:{x:number;y:number}; right:{x:number;y:number}; top:{x:number;y:number}; bottom:{x:number;y:number} };
      parentId: string | null
    }
  },
  parentId: string | null = null,
  containerRect: { left: number; top: number } = { left: 0, top: 0 }
) {
  if (!node || !node.ref || !node.ref.current) return;

  // Get the node's bounding rect relative to the viewport
  const rect = node.ref.current.getBoundingClientRect();

  // Guard against NaN/Infinity when node is display:none or detached
  const w = Number.isFinite(rect.width) ? rect.width : 0;
  const h = Number.isFinite(rect.height) ? rect.height : 0;
  const x0 = Number.isFinite(rect.left) ? rect.left : 0;
  const y0 = Number.isFinite(rect.top) ? rect.top : 0;

  // Calculate position relative to the container
  const x = x0 - containerRect.left;
  const y = y0 - containerRect.top;

  // Calculate anchor points relative to the container
  // Derive computed styles to account for padding/border so anchors sit exactly on the content edge
  const cs = window.getComputedStyle(node.ref.current);
  const padL = parseFloat(cs.paddingLeft || '0');
  const padR = parseFloat(cs.paddingRight || '0');
  const borL = parseFloat(cs.borderLeftWidth || '0');
  const borR = parseFloat(cs.borderRightWidth || '0');

  // Effective visual edges for connectors (outside border to outside border)
  const leftEdgeX = x + borL;         // just inside the left border
  const rightEdgeX = x + w - borR;    // just inside the right border
  const centerY = y + h / 2;

  const anchorsWorld = {
    center: { x: x + w / 2, y: centerY },
    left:   { x: leftEdgeX, y: centerY },
    right:  { x: rightEdgeX, y: centerY },
    top:    { x: x + w / 2, y: y },
    bottom: { x: x + w / 2, y: y + h },
  };

  positions[node.id] = {
    x, y, width: w, height: h, anchorsWorld, parentId,
  };

  // Recursively collect positions for all children
  node.children.forEach(child => collectNodePositions(child, positions, node.id, containerRect));
}

// Helper: render the timeline as a horizontal mind map with dynamic lines
function FloatingTimelineTree({ node, currentNode, onJump, depth = 0, nodeRefs }: { node: TimelineNode, currentNode: TimelineNode, onJump: (n: TimelineNode) => void, depth?: number, nodeRefs: { [key: string]: React.RefObject<HTMLDivElement> } }) {
  const thisRef = React.useRef<HTMLDivElement>(null);
  node.ref = thisRef as React.RefObject<HTMLDivElement>;
  if (nodeRefs) nodeRefs[node.id] = thisRef as React.RefObject<HTMLDivElement>;
  const hasChildren = node.children.length > 0;
  
  // Strip markdown for timeline preview
  const stripMarkdown = (text: string) => {
    return text
      .replace(/#{1,6}\s+/g, '') // Remove headers
      .replace(/\*\*(.*?)\*\*/g, '$1') // Remove bold
      .replace(/\*(.*?)\*/g, '$1') // Remove italic
      .replace(/`(.*?)`/g, '$1') // Remove inline code
      .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1') // Remove links
      .replace(/\n/g, ' ') // Replace newlines with spaces
      .trim();
  };
  
  const previewText = stripMarkdown(node.message).slice(0, 24) + (stripMarkdown(node.message).length > 24 ? '...' : '');
  
  // Check if this is a regenerated response (has -regenerated- in the id)
  const isRegenerated = node.id.includes('-regenerated-');
  
  return (
    <div style={{ display: 'flex', alignItems: 'center', position: 'relative', minHeight: 60 }}>
      <div ref={thisRef} style={{ zIndex: 2, position: 'relative' }}>
        {node.sender === 'user' ? (
          <span className="text-xs px-2 py-1 rounded font-mono italic text-muted-foreground bg-muted/40 cursor-default select-none opacity-70 shadow max-w-[200px] break-words">
            You: {previewText}
          </span>
        ) : (
          <button
            className={`text-xs px-2 py-1 rounded border shadow max-w-[200px] break-words ${node === currentNode ? 'bg-accent text-accent-foreground border-accent' : 'bg-muted/30 text-muted-foreground border-border hover:bg-accent/20'} transition`}
            onClick={() => onJump(node)}
            disabled={node === currentNode}
            // Prevent size changes on selection: lock height/weight via consistent font weight
            style={{ minWidth: 120, maxWidth: 200, fontWeight: 500 }}
            title={stripMarkdown(node.message)} // Show full text on hover
          >
            <div className="flex items-center gap-1">
              {isRegenerated && <span className="text-xs">🌿</span>}
              <span>{previewText}</span>
            </div>
          </button>
        )}
      </div>
      {hasChildren && (
        <div style={{ display: 'flex', flexDirection: 'column', marginLeft: 60, position: 'relative' }}>
          {node.children.map((child, i) => (
            <div key={child.id} style={{ marginTop: i === 0 ? 0 : 40 }}>
              {/* Avoid causing layout shift when selecting; only button styles change, not size */}
              <FloatingTimelineTree node={child} currentNode={currentNode} onJump={onJump} depth={depth + 1} nodeRefs={nodeRefs} />
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

interface RPGChatProps {
  onBackToDirectory?: () => void;
  parameters?: ChatParameters;
}

const RPGChat: React.FC<RPGChatProps> = ({ onBackToDirectory, parameters }) => {
  console.log('🎮 RPGChat initialized with parameters:', parameters);

  // Extract settingId for debugging
  const settingId = parameters?.settingType === 'character'
    ? parameters?.characterSetting?.id
    : parameters?.worldSetting?.id;
  console.log('🔍 Extracted settingId:', settingId, 'from settingType:', parameters?.settingType);
  // Initialize character based on parameters
  const [character, setCharacter] = useState<Character>(() => {
    if (!parameters) {
      return {
        id: '1',
        name: 'Adventurer',
        avatar: '🗡️',
        stats: {
          strength: 10,
          charm: 10,
          arcana: 10,
          luck: 10
        },
        inventory: [],
        relationships: [],
        level: 1,
        experience: 0
      };
    }

    const baseStats = 10;
    // No difficulty in ChatParameters, so default to 3
    const difficulty = 3;
    const statBonus = Math.max(0, difficulty - 3) * 2;

    let characterName = 'Adventurer';
    let characterClass = 'adventurer';
    if (parameters.settingType === 'character' && parameters.characterSetting) {
      characterName = parameters.characterSetting.name || characterName;
      characterClass = parameters.characterSetting.character || characterClass;
    } else if (parameters.settingType === 'world' && parameters.worldSetting) {
      characterName = parameters.worldSetting.characterRole || characterName;
      characterClass = parameters.worldSetting.characterRole || characterClass;
    }

    return {
      id: Date.now().toString(),
      name: characterName,
      avatar: getCharacterEmoji(characterClass),
      stats: {
        strength: baseStats + (characterClass === 'warrior' ? 3 : 0) + statBonus,
        charm: baseStats + (characterClass === 'bard' ? 3 : 0) + statBonus,
        arcana: baseStats + (characterClass === 'wizard' ? 3 : 0) + statBonus,
        luck: baseStats + statBonus
      },
      inventory: [],
      relationships: [],
      level: 1,
      experience: 0
    };
  });

  // Initialize empty messages array
  const [messages, setMessages] = useState<ChatMessage[]>([]);

  // Initialize game state based on parameters
  const [gameState, setGameState] = useState<GameState>(() => {
    if (!parameters) {
      return {
        currentScene: 'Unknown Location',
        sceneBackground: 'forest',
        mood: 'neutral',
        chapter: 'Chapter 1: The Beginning',
        questLog: ['Explore the world']
      };
    }

    let currentScene = 'Unknown Location';
    let sceneBackground = 'forest';
    let mood: GameState['mood'] = 'neutral';
    let questLog: string[] = ['Explore the world'];

    if (parameters.settingType === 'character' && parameters.characterSetting) {
      currentScene = parameters.characterSetting.scenario || currentScene;
      // No worldSetting, so fallback to 'forest'
      sceneBackground = 'forest';
      // No genre, so fallback to 'neutral'
      mood = 'neutral';
      // No quest, so fallback
      questLog = ['Explore the world'];
    } else if (parameters.settingType === 'world' && parameters.worldSetting) {
      currentScene = parameters.worldSetting.name || currentScene;
      sceneBackground = getSceneBackground(parameters.worldSetting.name || 'forest');
      mood = 'neutral';
      questLog = ['Explore the world'];
    }

    return {
      currentScene,
      sceneBackground,
      mood,
      chapter: 'Chapter 1: The Beginning',
      questLog
    };
  });

  const [timelineRoot, setTimelineRoot] = useState<TimelineNode>(() => {
    console.log('🎯 RPGChat: Initializing timeline with parameters:', parameters);
    
    // Check if we have a full timeline first (from reconstructed chat history)
    if (parameters && (parameters as any).fullTimeline) {
      const fullTimeline = (parameters as any).fullTimeline;
      console.log('🔄 RPGChat: Found full timeline from chat history:', fullTimeline);
      return fullTimeline;
    }
    
    // Check if we have existing chat data (fallback for single chat entry)
    if (parameters && (parameters as any).existingChat) {
      const existingChat = (parameters as any).existingChat;
      console.log('📄 RPGChat: Found existing chat data:', existingChat);
      
      // Create timeline from existing chat data
      const existingTimeline: TimelineNode = {
        id: 'existing-chat',
        message: existingChat.content || 'Welcome back to your adventure!',
        sender: 'ai',
        choices: existingChat.options?.map((option: string, index: number) => ({
          id: `existing-choice-${index}`,
          text: option,
          preview: option.toLowerCase()
        })) || [],
        children: [],
        parent: null
      };
      
      console.log('✅ RPGChat: Created existing timeline with choices:', existingTimeline.choices);
      return existingTimeline;
    }
    
    console.log('🆕 RPGChat: No existing chat, creating initial timeline');
    // Otherwise create initial timeline
    return createInitialTimeline(parameters);
  });

  // If we opened with an existing chat object, capture its id to ensure updates target same row
  useEffect(() => {
    if (!parameters) return;

    const maybeExisting = (parameters as any).existingChat;
    if (maybeExisting?.id && typeof maybeExisting.id === 'number') {
      console.log('🔗 Setting currentChatId from existing chat:', maybeExisting.id);
      setCurrentChatId(maybeExisting.id);
      return;
    }

    // If a full timeline was provided, try to find the root chat ID
    if ((parameters as any).fullTimeline) {
      const fullTimeline = (parameters as any).fullTimeline;
      if (fullTimeline?.chatId) {
        console.log('🔗 Setting currentChatId from timeline root:', fullTimeline.chatId);
        setCurrentChatId(fullTimeline.chatId);
        return;
      }

      // Best-effort resolve; ignore failures
      resolveOrCreateCurrentChatId().then(() => {}).catch(() => {});
    }
  }, [parameters]);
  
  const [currentNode, setCurrentNode] = useState<TimelineNode>(timelineRoot);
  const [isTyping, setIsTyping] = useState(false);
  const [showSavePanel, setShowSavePanel] = useState(false);
  const [showCharacterSheet, setShowCharacterSheet] = useState(true);
  const chatAreaRef = useRef<HTMLDivElement>(null);
  // Collapsible timeline state
  const [timelineOpen, setTimelineOpen] = useState(true);
  const [_, forceUpdate] = useState(0);

  // Reinitialize timeline when parameters change (start of a new chat in SPA)
  useEffect(() => {
    // Build a stable signature of parameters to decide when to reset
    const signature = (() => {
      if (!parameters) return 'none';
      const type = parameters.settingType;
      const id =
        type === 'character' ? parameters.characterSetting?.id : parameters.worldSetting?.id;
      return `${type}:${id ?? 'no-id'}`;
    })();

    // Maintain a ref of the last signature
    (RPGChat as any).__lastSignature = (RPGChat as any).__lastSignature ?? null;
    if ((RPGChat as any).__lastSignature !== signature) {
      (RPGChat as any).__lastSignature = signature;
      console.log('🆕 Parameters changed, resetting timeline to initial state for new chat:', signature);

      // Check if we have existing chat data or full timeline - if so, don't reset
      const hasExistingChat = parameters && (parameters as any).existingChat;
      const hasFullTimeline = parameters && (parameters as any).fullTimeline;
      
      if (hasExistingChat || hasFullTimeline) {
        console.log('🔄 Found existing chat data or full timeline, skipping timeline reset');
        return;
      }

      // Create a fresh initial timeline and reset related UI state
      const fresh = createInitialTimeline(parameters);
      setTimelineRoot(fresh);
      setCurrentNode(fresh);
      setAdditionalOptions([]);
      setCurrentChatId(null);
      setStreamingContent('');
      setIsStreamingResponse(false);
      setIsTyping(false);
      // Small tick to ensure ChoiceButtons remounts with fresh data
      forceUpdate(n => n + 1);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [parameters]);

  // Ensure currentNode is set correctly when timelineRoot changes (for existing chats)
  useEffect(() => {
    if (timelineRoot && timelineRoot !== currentNode) {
      console.log('🔄 Updating currentNode to match timelineRoot:', timelineRoot);
      
      // If we have a full timeline (existing chat), find the last AI node with choices
      if ((parameters as any)?.fullTimeline) {
        console.log('🔍 Looking for last AI node with choices in timeline');
        const findLastAINodeWithChoices = (node: TimelineNode): TimelineNode | null => {
          // If this node is an AI node with choices, it's a candidate
          if (node.sender === 'ai' && node.choices && node.choices.length > 0) {
            // Check if any children have AI nodes with choices
            let deepestChild: TimelineNode | null = null;
            for (const child of node.children) {
              const childResult = findLastAINodeWithChoices(child);
              if (childResult) {
                deepestChild = childResult;
              }
            }
            if (deepestChild) {
              return deepestChild;
            }
            // If no children have AI nodes with choices, this is the last one
            return node;
          }

          // Check children - if multiple AI siblings exist, prefer the one with highest chatId (most recent)
          let bestResult: TimelineNode | null = null;
          for (const child of node.children) {
            const result = findLastAINodeWithChoices(child);
            if (result) {
              if (!bestResult || (result.chatId && bestResult.chatId && result.chatId > bestResult.chatId)) {
                bestResult = result;
              } else if (!bestResult.chatId && result.chatId) {
                bestResult = result;
              }
            }
          }

          return bestResult;
        };
        
        const lastAINode = findLastAINodeWithChoices(timelineRoot);
        if (lastAINode) {
          console.log('✅ Found last AI node with choices:', lastAINode);
          setCurrentNode(lastAINode);
          return;
        }
      }
      
      // Fallback to root node
      setCurrentNode(timelineRoot);
    }
  }, [timelineRoot, parameters]); // Removed currentNode from dependencies to prevent interference

  // New state for enhanced chat features
  const [messageInput, setMessageInput] = useState('');
  const [isEnhancing, setIsEnhancing] = useState(false);
  const [isGeneratingOptions, setIsGeneratingOptions] = useState(false);
  const [additionalOptions, setAdditionalOptions] = useState<DialogueChoice[]>([]);
  const [isStreamingResponse, setIsStreamingResponse] = useState(false);
  const [streamingContent, setStreamingContent] = useState(''); // for rerendering after tree mutation
  const [svgLines, setSvgLines] = useState<{ x1: number; y1: number; x2: number; y2: number }[]>([]);
  // Track measured pixel size of the timeline content wrapper to keep SVG coordinate space exact
  const [svgSize, setSvgSize] = useState<{ width: number; height: number }>({ width: 0, height: 0 });
  const [epoch, setEpoch] = useState(0);
  let rafId = useRef<number | null>(null);
  let pending = useRef<boolean>(false);
  const [apiKeyError, setApiKeyError] = useState<string | null>(null);
  const [currentChatId, setCurrentChatId] = useState<number | null>(
    parameters?.currentChatId || null
  );

  console.log('💾 Initial currentChatId set to:', parameters?.currentChatId);
  const timelineContainerRef = useRef<HTMLDivElement>(null);
  const nodeRefs: { [key: string]: React.RefObject<HTMLDivElement> } = {};

  // Helper: resolve or create chatId for current AI node so we always update the same row
  const resolveOrCreateCurrentChatId = async (): Promise<number | null> => {
    if (currentChatId) return currentChatId;

    if (!parameters) return null;

    const settingId =
      parameters.settingType === 'character'
        ? parameters.characterSetting?.id
        : parameters.worldSetting?.id;

    if (!settingId) return null;

    try {
      // Try to find an existing chat row that matches the current node content
      const result = await window.electronAPI.getChats(settingId, parameters.settingType);
      if (result?.success && Array.isArray(result.chats)) {
        // Try to match by exact message content if JSON structure matches
        const match = result.chats.find((c: any) => {
          try {
            const parsed = JSON.parse(c.contents || '{}');
            return parsed?.content === currentNode.message;
          } catch {
            return false;
          }
        });

        if (match?.id) {
          setCurrentChatId(match.id);
          return match.id;
        }
      }
    } catch (e) {
      console.warn('Failed to resolve existing chat id, will attempt to create if needed:', e);
    }

    // As a last resort, if this screen was loaded with an existing AI node but there is no DB row,
    // create one so that subsequent updates append options to the same row.
    try {
      const chatContent = {
        content: currentNode.message,
        options: [...(currentNode.choices || [])].map((ch) => ch.text),
      };
      const saveRes = await window.electronAPI.saveChat(
        null, // Root chat has no parent
        settingId,
        parameters.settingType,
        JSON.stringify(chatContent),
        parameters.userPersona?.id || null,
        parameters.settingType === 'character' ? parameters.characterSetting?.id : null
      );
      if (saveRes?.success && saveRes.id) {
        // Update the current node with the chat ID
        currentNode.chatId = saveRes.id;
        setCurrentChatId(saveRes.id);
        return saveRes.id;
      }
    } catch (e) {
      console.error('Failed to create chat row while resolving currentChatId:', e);
    }

    return null;
  };

  // All nodes in the tree (for navigation)
  const allTimelineNodes = collectTimelineNodes(timelineRoot);

  // Handle choice selection (branching) - updated to use streaming
  const handleChoiceSelect = async (choice: DialogueChoice) => {
    setIsTyping(true);

    // Check if a user node for this choice already exists as a child of currentNode
    let userNode = currentNode.children.find(
      (child) => child.sender === 'user' && child.message === choice.text
    );

    if (!userNode) {
      // Create user node
      userNode = {
        id: Date.now().toString() + '-user',
        message: choice.text,
        sender: 'user',
        parent: currentNode,
        children: [],
        choiceId: choice.id
      };
      currentNode.children.push(userNode);

      // Immediately create an empty AI node and navigate to it
      const aiNode: TimelineNode = {
        id: Date.now().toString() + '-ai',
        message: '', // Start with empty message
        sender: 'ai',
        choices: [], // Will be populated after response
        parent: userNode,
        children: []
      };
      userNode.children.push(aiNode);
      setCurrentNode(aiNode);
      setAdditionalOptions([]); // Reset additional options

      // First, save the user's choice to database
      let userChatId: number | undefined = undefined;
      if (parameters) {
        const settingId = parameters.settingType === 'character'
          ? parameters.characterSetting?.id
          : parameters.worldSetting?.id;

        if (settingId) {
          const userChatContent = {
            content: choice.text,
            sender: 'user',
            isUserInput: true
          };

          try {
            console.log('💾 Saving user choice to database:', choice.text);
            console.log('🔍 Current node chat ID for user choice parent:', currentNode.chatId);
            console.log('🔍 Global currentChatId:', currentChatId);
            const parentChatId = currentNode.chatId || currentChatId;
            const userResult = await window.electronAPI.saveChat(
              parentChatId, // Parent is the current AI chat
              settingId,
              parameters.settingType,
              JSON.stringify(userChatContent),
              parameters.userPersona?.id || null,
              parameters.settingType === 'character' ? parameters.characterSetting?.id : null
            );
            if (userResult.success) {
              userChatId = userResult.id;
              userNode.chatId = userChatId;
              console.log('✅ User choice saved with ID:', userChatId);
            }
          } catch (error) {
            console.error('Failed to save user choice:', error);
          }
        }
      }

      try {
        // Generate AI response with streaming directly into the node
        const aiResponse = await generateAIResponseWithStreaming(choice.text, aiNode);

        // Generate choices for the response
        const choices = await generateChoicesForResponse(aiResponse, choice.text);

        // Update the AI node with choices
        const newChoices = choices.map((option: string, index: number) => ({
          id: `choice-${Date.now()}-${index}`,
          text: option,
          preview: option.toLowerCase()
        }));
        aiNode.choices = newChoices;

        // Ensure currentNode stays on the AI node after streaming finishes
        setCurrentNode(aiNode);

        // Now save the AI response to database with user choice as parent
        if (parameters && userChatId) {
          const settingId = parameters.settingType === 'character'
            ? parameters.characterSetting?.id
            : parameters.worldSetting?.id;

          if (settingId) {
            const aiChatContent = {
              content: aiResponse,
              options: choices,
              sender: 'ai'
            };

            try {
              console.log('💾 Saving AI response to database with parent:', userChatId);
              const aiResult = await window.electronAPI.saveChat(
                userChatId, // Parent is the user's choice
                settingId,
                parameters.settingType,
                JSON.stringify(aiChatContent),
                parameters.userPersona?.id || null,
                parameters.settingType === 'character' ? parameters.characterSetting?.id : null
              );
              if (aiResult.success) {
                // Update the AI node with the new chat ID
                aiNode.chatId = aiResult.id;
                setCurrentChatId(aiResult.id);
                console.log('✅ AI response saved with ID:', aiResult.id);
              }
            } catch (error) {
              console.error('Failed to save AI response:', error);
            }
          }
        }

      } catch (error) {
        console.error('Failed to generate AI response:', error);
        // Remove the user node if AI response failed
        currentNode.children.pop();
        // Reset to the original node if AI response failed
        setCurrentNode(currentNode);
      } finally {
        setIsTyping(false);
        forceUpdate((n) => n + 1); // force rerender to update tree
      }
    } else {
      // If already exists, jump to the existing branch
      setCurrentNode(userNode.children[0]);
      setIsTyping(false);
    }
  };

  // Generate AI response with streaming and structured JSON
  const generateAIResponseWithStreaming = async (userMessage: string, aiNode?: TimelineNode): Promise<string> => {
    const apiKey = await getOpenRouterApiKey();
    if (!apiKey) {
      throw new Error('No API key found');
    }

    const context = buildChatContext();
    // Check if user persona is available
    const hasUserPersona = parameters?.userPersona;
    
    const prompt = `Based on this context:
${context}

The user said: "${userMessage}"

${hasUserPersona ? 
  `IMPORTANT: You are responding to ${parameters.userPersona?.name || 'the user'} who is roleplaying as their persona. Consider their personality, background, and perspective when crafting your response.` :
  `Respond naturally and engagingly to the user's message.`
}

Focus on providing a detailed, contextually appropriate response that advances the conversation. 

You can use markdown formatting to enhance your response:
- Use **bold** for emphasis on important words or actions
- Use *italic* for thoughts, emotions, or subtle details
- Use \`code\` for special terms, names, or technical references
- Use > blockquotes for dramatic dialogue or important statements
- Use lists for describing multiple items or actions

Do not include any choice options or JSON formatting - just respond as the character or world would naturally respond with rich, immersive storytelling.`;

    // Use direct API call with streaming for real-time response
    return await generateAIResponseDirect(userMessage, prompt, aiNode);
  };

  const generateAIResponseDirect = async (userMessage: string, prompt: string, aiNode?: TimelineNode): Promise<string> => {
    const apiKey = await getOpenRouterApiKey();
    if (!apiKey) {
      throw new Error('No API key found');
    }

    // Add markdown instructions to the prompt if not already present
    const enhancedPrompt = prompt.includes('markdown formatting') ? prompt : `${prompt}

You can use markdown formatting to enhance your response:
- Use **bold** for emphasis on important words or actions
- Use *italic* for thoughts, emotions, or subtle details
- Use \`code\` for special terms, names, or technical references
- Use > blockquotes for dramatic dialogue or important statements
- Use lists for describing multiple items or actions

${parameters?.userPersona ? 
  `IMPORTANT: You are responding to ${parameters.userPersona.name} who is roleplaying as their persona. Consider their personality, background, and perspective when crafting your response.` :
  ''
}`;

    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': window.location.origin,
        'X-Title': 'Electrobox Chat'
      },
      body: JSON.stringify({
        model: 'deepseek/deepseek-chat-v3-0324',
        messages: [{ role: 'user', content: enhancedPrompt }],
        max_tokens: 10000,
        temperature: 0.7,
        stream: true
      })
    });

    if (!response.ok || !response.body) {
      throw new Error('Failed to get AI response');
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let fullContent = '';
    let buffer = '';

    setIsStreamingResponse(true);
    setStreamingContent('');

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const dataStr = line.slice(6).trim();
            if (dataStr === '[DONE]') continue;

            try {
              const data = JSON.parse(dataStr);
              const delta = data.choices?.[0]?.delta?.content || '';
              if (delta) {
                fullContent += delta;
                setStreamingContent(fullContent);
                
                // Update the AI node's message in real-time if provided
                if (aiNode) {
                  aiNode.message = fullContent;
                  console.log('Streaming update:', fullContent.length, 'characters');
                }
                // Update streaming content state for immediate UI updates
                setStreamingContent(fullContent);
                forceUpdate((n) => n + 1); // Force re-render to show streaming content
              }
            } catch (e) {
              // Ignore parsing errors for individual chunks
            }
          }
        }
      }

      return fullContent || "I continue the conversation...";

    } finally {
      setIsStreamingResponse(false);
      setStreamingContent('');
    }
  };

  const generateChoicesForResponse = async (aiResponse: string, userMessage: string): Promise<string[]> => {
    const apiKey = await getOpenRouterApiKey();
    if (!apiKey) {
      throw new Error('No API key found');
    }

    const context = buildChatContext();
    
    // Check if user persona is available
    const hasUserPersona = parameters?.userPersona;
    
    const prompt = `Based on this context:
${context}

The user said: "${userMessage}"
The AI responded: "${aiResponse}"

    ${hasUserPersona ? 
      `IMPORTANT: Generate 4 different choice options that the USER'S PERSONA (${parameters.userPersona?.name || 'User'}) would naturally choose to say or do in response to the AI's message. Each option should reflect the user persona's personality, background, and perspective.` :
      `Generate 4 different choice options that the user could select to continue the conversation.`
    }

Each option should be verbose and represent different ways the user could respond.

You MUST respond with ONLY valid JSON in this exact format - no other text, no explanations:
{
  "options": [
    "option 1 text ",
    "option 2 text ", 
    "option 3 text ",
    "option 4 text "
  ]
}

CRITICAL: Start your response with { and end with }. Do not include any text before or after the JSON. The options should be varied and interesting ways the user could respond next.`;

    // Use the AI service with the choice_generation prompt type
    try {
      const response = await window.electronAPI.sendToAI({
        message: prompt,
        promptType: 'choice_generation'
      });
      
      if (response.success) {
        // Parse the JSON response
        try {
          // Extract JSON from potential markdown code blocks
          let jsonContent = response.response.trim();
          
          // Remove markdown code block markers if present
          if (jsonContent.startsWith('```json')) {
            jsonContent = jsonContent.replace(/^```json\s*/, '');
          }
          if (jsonContent.startsWith('```')) {
            jsonContent = jsonContent.replace(/^```\s*/, '');
          }
          if (jsonContent.endsWith('```')) {
            jsonContent = jsonContent.replace(/\s*```$/, '');
          }
          
          // Find the first { and last } to extract JSON
          const firstBrace = jsonContent.indexOf('{');
          const lastBrace = jsonContent.lastIndexOf('}');
          
          if (firstBrace !== -1 && lastBrace !== -1 && lastBrace > firstBrace) {
            jsonContent = jsonContent.substring(firstBrace, lastBrace + 1);
          }
          
          const jsonResponse = JSON.parse(jsonContent);
          if (jsonResponse.options && Array.isArray(jsonResponse.options)) {
            return jsonResponse.options.slice(0, 4);
          }
        } catch (e) {
          console.error('Failed to parse choices JSON response:', e);
        }
      } else {
        throw new Error(response.error || 'Failed to get choices response');
      }
    } catch (error) {
      console.error('AI service error for choices:', error);
      // Fallback to direct API call
      return await generateChoicesDirect(aiResponse, userMessage, prompt);
    }

    // Fallback
    return ["Continue", "Ask question", "Take action", "Observe"];
  };

  const generateChoicesDirect = async (aiResponse: string, userMessage: string, prompt: string): Promise<string[]> => {
    const apiKey = await getOpenRouterApiKey();
    if (!apiKey) {
      throw new Error('No API key found');
    }

    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': window.location.origin,
        'X-Title': 'Electrobox Chat'
      },
      body: JSON.stringify({
        model: 'deepseek/deepseek-chat-v3-0324',
        messages: [{ role: 'user', content: prompt }],
        max_tokens: 1000,
        temperature: 0.7,
        response_format: { type: 'json_object' }
      })
    });

    if (!response.ok) {
      throw new Error('Failed to get choices response');
    }

    const responseData = await response.json();
    const content = responseData.choices?.[0]?.message?.content || '';

    try {
      // Try to extract JSON from the response if it contains extra text
      let jsonContent = content.trim();
      
      // Find the first { and last } to extract JSON
      const firstBrace = jsonContent.indexOf('{');
      const lastBrace = jsonContent.lastIndexOf('}');
      
      if (firstBrace !== -1 && lastBrace !== -1 && lastBrace > firstBrace) {
        jsonContent = jsonContent.substring(firstBrace, lastBrace + 1);
      }
      
      const jsonResponse = JSON.parse(jsonContent);
      if (jsonResponse.options && Array.isArray(jsonResponse.options)) {
        return jsonResponse.options.slice(0, 4);
      }
    } catch (e) {
      console.error('Failed to parse choices JSON response:', e);
      console.log('Raw choices response:', content);
    }

    // Fallback if JSON parsing fails
    return ["Continue", "Ask question", "Take action", "Observe"];
  };

  const generateAIResponse = async (choice: DialogueChoice, parameters?: ChatParameters): Promise<string> => {
    // Legacy function - now calls the streaming version
    try {
      const result = await generateAIResponseWithStreaming(choice.text);
      return result;
    } catch (error) {
      console.error('AI response generation failed:', error);
      return "Your choice leads you forward in your journey...";
    }
  };

  const generateNewChoices = async (previousChoiceId: string, parameters?: ChatParameters): Promise<DialogueChoice[]> => {
    // Generate contextual choices based on the previous choice and world parameters
    const choiceMap: { [key: string]: DialogueChoice[] } = {
      explore: [
        { id: 'investigate', text: 'Investigate further', preview: 'look deeper into the area' },
        { id: 'move', text: 'Move to a new area', preview: 'explore elsewhere' },
        { id: 'rest', text: 'Take a moment to rest', preview: 'recover and plan' }
      ],
      quest: [
        { id: 'advance', text: 'Advance toward your goal', preview: 'make progress on your quest' },
        { id: 'gather', text: 'Gather information', preview: 'learn more about your objective' },
        { id: 'prepare', text: 'Prepare for challenges', preview: 'get ready for what lies ahead' }
      ],
      interact: [
        { id: 'approach', text: 'Approach any people you see', preview: 'make contact with NPCs' },
        { id: 'observe', text: 'Observe from a distance', preview: 'watch and learn' },
        { id: 'signal', text: 'Signal for help', preview: 'try to attract attention' }
      ],
      prepare: [
        { id: 'equipment', text: 'Check your equipment', preview: 'review your gear' },
        { id: 'abilities', text: 'Review your abilities', preview: 'consider your skills' },
        { id: 'plan', text: 'Make a plan', preview: 'think about your next steps' }
      ],
      start: [
        { id: 'explore', text: 'Explore the area', preview: 'investigate your surroundings' },
        { id: 'quest', text: 'Begin your quest', preview: 'start your main objective' },
        { id: 'interact', text: 'Look for others', preview: 'seek out NPCs or companions' }
      ]
    };

    return choiceMap[previousChoiceId] || [
      { id: 'continue', text: 'Continue your journey', preview: 'move forward' }
    ];
  };

  // Get OpenRouter API key
  const getOpenRouterApiKey = async (): Promise<string | null> => {
    try {
      const result = await window.electronAPI.getActiveApiKey();
      if (result.success && result.apiKey) {
        return result.apiKey.api_key;
      }
      console.error('No API key found in database');
      return null;
    } catch (error) {
      console.error('Failed to get API key:', error);
      return null;
    }
  };

  // Build chat context for AI prompts
  const buildChatContext = (): string => {
    let context = '';

    if (parameters?.settingType === 'character' && parameters.characterSetting) {
      const char = parameters.characterSetting;
      context = `Character: ${char.name}
Personality: ${char.personality || 'Not specified'}
Scenario: ${char.scenario || 'Not specified'}
Current situation: ${currentNode.message}`;
    } else if (parameters?.settingType === 'world' && parameters.worldSetting) {
      const world = parameters.worldSetting;
      context = `World: ${world.name}
World Description: ${world.worldDescription || 'Not specified'}
Character Role: ${world.characterRole || 'Not specified'}
Current situation: ${currentNode.message}`;
    } else {
      context = `Current situation: ${currentNode.message}`;
    }

    // Add user persona information if available
    if (parameters?.userPersona) {
      const persona = parameters.userPersona;
      context += `\n\nUSER PERSONA (who the user is roleplaying as):
Name: ${persona.name}
Personality: ${persona.personality || 'Not specified'}
Appearance: ${persona.appearance || 'Not specified'}`;
    }

    return context;
  };

  // Enhanced prompt functionality
  const handleEnhancePrompt = async () => {
    if (!messageInput.trim()) return;

    setIsEnhancing(true);
    try {
      const apiKey = await getOpenRouterApiKey();
      if (!apiKey) {
        console.error('No API key found');
        return;
      }

      const context = buildChatContext();
      // Check if user persona is available
      const hasUserPersona = parameters?.userPersona;
      
      const prompt = `Based on this chat context:
${context}

The user has written: "${messageInput}"

${hasUserPersona ? 
  `IMPORTANT: Enhance this message as if it's coming from ${parameters.userPersona?.name || 'the user'} who is roleplaying as their persona. Consider their personality, background, and perspective when improving the message.` :
  `Enhance and improve this message to be more engaging, detailed, and contextually appropriate.`
}

Keep the user's intent but make it more immersive and well-written. Return only the enhanced message, no explanations.`;

      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': window.location.origin,
          'X-Title': 'Electrobox Chat'
        },
        body: JSON.stringify({
          model: 'deepseek/deepseek-chat-v3-0324',
          messages: [{ role: 'user', content: prompt }],
          max_tokens: 1000,
          temperature: 0.7
        })
      });

      if (response.ok) {
        const data = await response.json();
        const enhancedText = data.choices?.[0]?.message?.content?.trim();
        if (enhancedText) {
          setMessageInput(enhancedText);
        }
      }
    } catch (error) {
      console.error('Failed to enhance prompt:', error);
    } finally {
      setIsEnhancing(false);
    }
  };

  // Generate more options functionality
  const handleGenerateMoreOptions = async () => {
    setIsGeneratingOptions(true);
    try {
      const apiKey = await getOpenRouterApiKey();
      if (!apiKey) {
        console.error('No API key found');
        return;
      }

      const context = buildChatContext();
      const existingOptions = [...(currentNode.choices || []), ...additionalOptions];
      const existingTexts = existingOptions.map(opt => opt.text).join(', ');

      // Check if user persona is available
      const hasUserPersona = parameters?.userPersona;
      
      const prompt = `Based on this chat context:
${context}

Existing options: ${existingTexts}

${hasUserPersona ? 
  `IMPORTANT: Generate exactly 4 NEW contextual response options that the USER'S PERSONA (${parameters.userPersona?.name || 'User'}) would naturally choose to say or do. These should be different from the existing options and reflect the user persona's personality, background, and perspective.` :
  `Generate exactly 4 NEW contextual response options that are different from the existing ones. Each option should be verbose and offer unique ways to respond or act.`
}

Respond with a JSON object in this format:
{
  "options": [
    "option 1 text",
    "option 2 text", 
    "option 3 text",
    "option 4 text"
  ]
}`;

      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': window.location.origin,
          'X-Title': 'Electrobox Chat'
        },
        body: JSON.stringify({
          model: 'deepseek/deepseek-chat-v3-0324',
          messages: [{ role: 'user', content: prompt }],
          max_tokens: 1000,
          temperature: 0.8,
          response_format: { type: 'json_object' }
        })
      });

      if (response.ok) {
        const data = await response.json();
        const generatedContent = data.choices?.[0]?.message?.content?.trim();

        if (generatedContent) {
          try {
            // Extract JSON from potential markdown code blocks
            let jsonContent = generatedContent.trim();
            
            // Remove markdown code block markers if present
            if (jsonContent.startsWith('```json')) {
              jsonContent = jsonContent.replace(/^```json\s*/, '');
            }
            if (jsonContent.startsWith('```')) {
              jsonContent = jsonContent.replace(/^```\s*/, '');
            }
            if (jsonContent.endsWith('```')) {
              jsonContent = jsonContent.replace(/\s*```$/, '');
            }
            
            // Find the first { and last } to extract JSON
            const firstBrace = jsonContent.indexOf('{');
            const lastBrace = jsonContent.lastIndexOf('}');
            
            if (firstBrace !== -1 && lastBrace !== -1 && lastBrace > firstBrace) {
              jsonContent = jsonContent.substring(firstBrace, lastBrace + 1);
            }
            
            // Parse the JSON response
            const responseData = JSON.parse(jsonContent);
            
            if (responseData.options && Array.isArray(responseData.options)) {
              const newOptions = responseData.options
                .slice(0, 4)
                .map((text: string, index: number) => ({
                  id: `generated-${Date.now()}-${index}`,
                  text: String(text),
                  preview: String(text).toLowerCase()
                }));

              if (newOptions.length > 0) {
                setAdditionalOptions(prev => [...prev, ...newOptions]);
                
                // Save the combined options (original + additional) to database
                if (parameters) {
                  const settingId = parameters.settingType === 'character'
                    ? parameters.characterSetting?.id
                    : parameters.worldSetting?.id;

                  if (settingId) {
                    const allOptions = [
                      ...(currentNode.choices || []).map(choice => choice.text),
                      ...additionalOptions.map(choice => choice.text),
                      ...newOptions.map(choice => choice.text)
                    ];

                    const chatContent = {
                      content: currentNode.message,
                      options: allOptions
                    };

                    try {
                      // Always resolve or create the chat id before updating
                      const resolvedId = await resolveOrCreateCurrentChatId();
                      if (resolvedId) {
                        await window.electronAPI.updateChat(
                          resolvedId,
                          JSON.stringify(chatContent)
                        );
                        console.log('✅ Updated existing chat with new options');
                      } else {
                        console.warn('⚠️ Could not resolve chat id; skipped persistence to avoid duplicate rows');
                      }
                    } catch (error) {
                      console.error('Failed to save updated chat options:', error);
                    }
                  }
                }
              }
            }
          } catch (e) {
            console.error('Failed to parse JSON options:', e);
            console.log('Raw response:', generatedContent);
          }
        }
      }
    } catch (error) {
      console.error('Failed to generate more options:', error);
    } finally {
      setIsGeneratingOptions(false);
    }
  };

  // Handle regenerate AI response - creates a new branching node
  const handleRegenerateResponse = async () => {
    if (currentNode.sender !== 'ai') return;
    
    setIsTyping(true);
    
    try {
      // Get the user's previous message that led to this AI response
      const userMessage = currentNode.parent?.message || "Continue the conversation";
      
      // Create a new AI node as a sibling to the current one
      const newAiNode: TimelineNode = {
        id: Date.now().toString() + '-regenerated-ai',
        message: '', // Start with empty message for streaming
        sender: 'ai',
        choices: [], // Will be populated after response
        parent: currentNode.parent, // Same parent as current node
        children: []
      };
      
      // Add the new AI node as a sibling to the current node
      if (currentNode.parent) {
        currentNode.parent.children.push(newAiNode);
      }
      
      // Navigate to the new node
      setCurrentNode(newAiNode);
      setAdditionalOptions([]); // Reset additional options
      
      // Generate new AI response with streaming directly into the new node
      const newAiResponse = await generateAIResponseWithStreaming(userMessage, newAiNode);
      
      // Generate new choices for the response
      const newChoices = await generateChoicesForResponse(newAiResponse, userMessage);
      
      // Update the new AI node with choices
      const updatedChoices = newChoices.map((option: string, index: number) => ({
        id: `regenerated-${Date.now()}-${index}`,
        text: option,
        preview: option.toLowerCase()
      }));
      
      newAiNode.choices = updatedChoices;
      
      // Ensure currentNode stays on the new AI node
      setCurrentNode(newAiNode);
      
      // Save the new branching interaction to database
      if (parameters) {
        const settingId = parameters.settingType === 'character'
          ? parameters.characterSetting?.id
          : parameters.worldSetting?.id;

        if (settingId) {
          const chatContent = {
            content: newAiResponse,
            options: newChoices,
            sender: 'ai',
            userChoice: userMessage,
            isRegenerated: true // Mark as regenerated response
          };

          try {
            // Use the parent user message's chat ID as parent for proper branching
            const parentId = currentNode.parent?.chatId || null;
            console.log('🔄 Regenerating AI response with parent ID:', parentId, 'from user message:', currentNode.parent?.message?.substring(0, 50));
            const result = await window.electronAPI.saveChat(
              parentId, // Use parent user message as parent for proper branching
              settingId,
              parameters.settingType,
              JSON.stringify(chatContent),
              parameters.userPersona?.id || null,
              parameters.settingType === 'character' ? parameters.characterSetting?.id : null
            );
            if (result.success) {
              // Update the new AI node with the chat ID
              newAiNode.chatId = result.id;
              setCurrentChatId(result.id);
              console.log('✅ Regenerated AI response saved with ID:', result.id, 'and parent ID:', parentId);
            }
          } catch (error) {
            console.error('Failed to save regenerated chat interaction:', error);
          }
        }
      }
      
    } catch (error) {
      console.error('Failed to regenerate AI response:', error);
      // If regeneration failed, stay on the original node
      setCurrentNode(currentNode);
    } finally {
      setIsTyping(false);
      forceUpdate((n) => n + 1); // force rerender to update tree
    }
  };

  // Handle custom user message submission
  const handleSubmitMessage = async () => {
    if (!messageInput.trim()) return;

    // Preserve original input so it doesn't "vanish" and can be restored on failure
    const originalInput = messageInput.trim();

    setIsTyping(true);

    // Create user node
    const userNode: TimelineNode = {
      id: Date.now().toString() + '-user',
      message: originalInput,
      sender: 'user',
      parent: currentNode,
      children: []
    };
    currentNode.children.push(userNode);

    // Immediately create an empty AI node and navigate to it
    const aiNode: TimelineNode = {
      id: Date.now().toString() + '-ai',
      message: '', // Start with empty message
      sender: 'ai',
      choices: [], // Will be populated after response
      parent: userNode,
      children: []
    };
    userNode.children.push(aiNode);
    setCurrentNode(aiNode);
    setAdditionalOptions([]); // Reset additional options

    // First, save the user's custom message to database
    let userChatId: number | undefined = undefined;
    if (parameters) {
      const settingId = parameters.settingType === 'character'
        ? parameters.characterSetting?.id
        : parameters.worldSetting?.id;

      if (settingId) {
        const userChatContent = {
          content: originalInput,
          sender: 'user',
          isUserInput: true
        };

        try {
          console.log('💾 Saving user message to database:', originalInput);
          console.log('🔍 Current node chat ID for user message parent:', currentNode.chatId);
          console.log('🔍 Global currentChatId:', currentChatId);
          const parentChatId = currentNode.chatId || currentChatId;
          const userResult = await window.electronAPI.saveChat(
            parentChatId, // Parent is the current AI chat
            settingId,
            parameters.settingType,
            JSON.stringify(userChatContent),
            parameters.userPersona?.id || null,
            parameters.settingType === 'character' ? parameters.characterSetting?.id : null
          );
          if (userResult.success) {
            userChatId = userResult.id;
            userNode.chatId = userChatId;
            console.log('✅ User message saved with ID:', userChatId);
          }
        } catch (error) {
          console.error('Failed to save user message:', error);
        }
      }
    }

    try {
      // Generate AI response with streaming directly into the node
      const aiResponse = await generateAIResponseWithStreaming(originalInput, aiNode);

      // Generate choices for the response
      const choices = await generateChoicesForResponse(aiResponse, originalInput);

      // Update the AI node with choices
      const newChoices = choices.map((option: string, index: number) => ({
        id: `response-${Date.now()}-${index}`,
        text: option,
        preview: option.toLowerCase()
      }));
      aiNode.choices = newChoices;

      // Ensure currentNode stays on the AI node after streaming finishes
      setCurrentNode(aiNode);

      // Now save the AI response to database with user message as parent
      if (parameters && userChatId) {
        const settingId = parameters.settingType === 'character'
          ? parameters.characterSetting?.id
          : parameters.worldSetting?.id;

        if (settingId) {
          const aiChatContent = {
            content: aiResponse,
            options: choices,
            sender: 'ai'
          };

          try {
            console.log('💾 Saving AI response to database with parent:', userChatId);
            const aiResult = await window.electronAPI.saveChat(
              userChatId, // Parent is the user's message
              settingId,
              parameters.settingType,
              JSON.stringify(aiChatContent),
              parameters.userPersona?.id || null,
              parameters.settingType === 'character' ? parameters.characterSetting?.id : null
            );
            if (aiResult.success) {
              // Update the AI node with the new chat ID
              aiNode.chatId = aiResult.id;
              setCurrentChatId(aiResult.id);
              console.log('✅ AI response saved with ID:', aiResult.id);
            }
          } catch (error) {
            console.error('Failed to save AI response:', error);
          }
        }
      }

      // Only clear the input after we successfully processed and updated choices
      setMessageInput('');

    } catch (error) {
      console.error('Failed to process message:', error);
      // Remove the user node if AI response failed
      currentNode.children.pop();
      // Reset to the original node if AI response failed
      setCurrentNode(currentNode);
      // Restore the input so the user doesn't lose their message
      setMessageInput(originalInput);
    } finally {
      setIsTyping(false);
      forceUpdate((n) => n + 1);
    }
  };

  const updateCharacterProgress = (choiceId: string) => {
    setCharacter(prev => ({
      ...prev,
      experience: prev.experience + 10,
      stats: {
        ...prev.stats,
        // Add small stat improvements based on choices
        strength: choiceId === 'advance' ? prev.stats.strength + 1 : prev.stats.strength,
        charm: choiceId === 'approach' ? prev.stats.charm + 1 : prev.stats.charm,
        arcana: choiceId === 'investigate' ? prev.stats.arcana + 1 : prev.stats.arcana,
        luck: choiceId === 'signal' ? prev.stats.luck + 1 : prev.stats.luck
      }
    }));
  };

  const handleSaveGame = () => {
    const saveData = {
      character,
      messages,
      gameState,
      timestamp: Date.now()
    };
    localStorage.setItem('rpgChatSave', JSON.stringify(saveData));
    console.log('Game saved!');
  };

  const handleLoadGame = () => {
    const savedData = localStorage.getItem('rpgChatSave');
    if (savedData) {
      const parsed = JSON.parse(savedData);
      setCharacter(parsed.character);
      setMessages(parsed.messages);
      setGameState(parsed.gameState);
      console.log('Game loaded!');
    }
  };



  useEffect(() => {
    if (chatAreaRef.current) {
      chatAreaRef.current.scrollTop = chatAreaRef.current.scrollHeight;
    }
  }, [messages]);

  // Add logging for component mount and state
  useEffect(() => {
    console.log('🚀 RPGChat component mounted');
    console.log('📊 Current state:', {
      character,
      gameState,
      timelineRoot,
      currentNode,
      parameters
    });
  }, []);

  // Debug: log messageInput changes and state transitions
  useEffect(() => {
    console.log('[DEBUG] messageInput changed', { value: messageInput });
  }, [messageInput]);

  useEffect(() => {
    console.log('[DEBUG] isTyping changed', { isTyping });
  }, [isTyping]);

  useEffect(() => {
    console.log('[DEBUG] isStreamingResponse changed', { isStreamingResponse });
  }, [isStreamingResponse]);

  // Log when currentNode changes
  useEffect(() => {
    console.log('🔄 Current node changed to:', currentNode);
  }, [currentNode]);

  // Handle timeline navigation (jump to any node)
  const handleJumpToNode = (node: TimelineNode) => {
    setCurrentNode(node);
  };

  // Timeline path for breadcrumb
  const timelinePath = getTimelinePathToNode(currentNode);

  // After render, measure node positions and set SVG lines
  // IMPORTANT: Only depend on timeline structure and container metrics,
  // not on currentNode selection to avoid line jitter when clicking nodes.
  useLayoutEffect(() => {
    if (!timelineOpen || !timelineContainerRef.current) return;

    // bump epoch to coalesce bursts
    setEpoch(e => e + 1);
    if (pending.current) return;
    pending.current = true;

    const doMeasureAndRoute = () => {
      pending.current = false;
      if (!timelineContainerRef.current) return;

      // Get the timeline content wrapper that contains both the SVG and the nodes
      const contentEl = timelineContainerRef.current.querySelector('#timeline-content') as HTMLElement | null;
      if (!contentEl) return;

      // Measure the content wrapper's bounding rect to establish the coordinate system
      const containerRect = contentEl.getBoundingClientRect();

      // Persist exact pixel size so the SVG uses the same coordinate space
      const nextSize = { width: Math.round(containerRect.width), height: Math.round(containerRect.height) };
      setSvgSize(prev => (prev.width === nextSize.width && prev.height === nextSize.height ? prev : nextSize));
      
      const positions: {
        [key: string]: {
          x: number; y: number; width: number; height: number;
          anchorsWorld: { center:{x:number;y:number}; left:{x:number;y:number}; right:{x:number;y:number}; top:{x:number;y:number}; bottom:{x:number;y:number} };
          parentId: string | null
        }
      } = {};

      // Collect node positions relative to the content wrapper
      collectNodePositions(timelineRoot, positions, null, containerRect);

      // No manual padding offset; positions are already container-relative

      // Build lines from parent's right-midpoint to child's left-midpoint; add small horizontal nudge
      // to avoid rendering inside rounded borders.
      const lines: { x1: number; y1: number; x2: number; y2: number }[] = [];
      Object.values(positions).forEach(pos => {
        if (pos.parentId && positions[pos.parentId]) {
          const parent = positions[pos.parentId];

          const src = { ...parent.anchorsWorld.right };
          const dst = { ...pos.anchorsWorld.left };

          // Nudge 1px outward away from the node box to keep the stroke off the border radius
          src.x += 1;
          dst.x -= 1;

          const snap = (v: number) => Math.round(v) + 0.5;

          lines.push({
            x1: snap(src.x),
            y1: snap(src.y),
            x2: snap(dst.x),
            y2: snap(dst.y),
          });
        }
      });

      setSvgLines(prev => {
        // Avoid unnecessary state churn when geometry is identical
        const sameLen = prev.length === lines.length;
        const same =
          sameLen &&
          prev.every((l, i) =>
            l.x1 === lines[i].x1 &&
            l.y1 === lines[i].y1 &&
            l.x2 === lines[i].x2 &&
            l.y2 === lines[i].y2
          );
        return same ? prev : lines;
      });
    };

    // Trailing-edge RAF debounce
    if (rafId.current) cancelAnimationFrame(rafId.current);
    rafId.current = requestAnimationFrame(doMeasureAndRoute);

    return () => {
      if (rafId.current) cancelAnimationFrame(rafId.current);
      rafId.current = null;
      pending.current = false;
    };
  // DO NOT include currentNode in deps to prevent re-routing on selection changes
  }, [timelineRoot, timelineOpen]);

  return (
    <div className="min-h-screen bg-gradient-background flex overflow-x-hidden">
      <div className="flex-1 flex flex-col overflow-x-hidden">
        {/* Top Bar */}
        <div className="h-16 bg-card border-b border-border flex items-center justify-between px-4 shadow-accent">
          <div className="flex items-center gap-3">
            {/* Back to Directory Button */}
            <Button
              variant="secondary"
              size="sm"
              className="mr-2"
              onClick={onBackToDirectory || (() => window.location.reload())}
              title="Back to Main Screen"
            >
              <ArrowLeft className="h-4 w-4 mr-1" />
              Back
            </Button>
            <div>
              <h2 className="font-bold text-foreground">
                {parameters?.settingType === 'character' && parameters.characterSetting 
                  ? parameters.characterSetting.name 
                  : parameters?.settingType === 'world' && parameters.worldSetting 
                    ? parameters.worldSetting.name 
                    : character.name}
              </h2>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setTimelineOpen((open) => !open)}
              title={timelineOpen ? 'Collapse timeline' : 'Expand timeline'}
            >
              {timelineOpen ? 'Hide Timeline' : 'Show Timeline'}
            </Button>
            <Button
              variant="outline"
              size="sm"
            >
              <Settings className="h-4 w-4" />
            </Button>
            {/* Debug: Quick state snapshot button */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                console.log('[DEBUG] Snapshot', {
                  messageInput,
                  isTyping,
                  isStreamingResponse,
                  currentNodeSender: currentNode.sender,
                  currentNodeMessageLength: (currentNode.message || '').length,
                  hasChoices: (currentNode.choices || []).length,
                });
              }}
              title="Log debug snapshot to console"
            >
              Debug
            </Button>
          </div>
        </div>

        {/* Collapsible Timeline Breadcrumb */}
        {timelineOpen && (
          <div
            className="w-full overflow-x-auto overflow-y-visible px-0 py-2 bg-background/80 border-b border-border"
            // Establish a stable positioning context to align SVG (edge layer) and nodes
            style={{ maxHeight: 420, minHeight: 80, position: 'relative' }}
            ref={timelineContainerRef}
          >
            {/* A single inner content wrapper that both nodes and SVG measure against */}
            <div
              id="timeline-content"
              style={{
                position: 'relative',
                display: 'inline-block',
                // ensure a predictable padding box so anchors match the same origin
                padding: '8px 0',
              }}
            >
              {/* SVG lines overlay aligned to the same wrapper */}
              <svg
                // Keep the SVG's internal coordinate system exactly equal to the measured wrapper size
                width={svgSize.width || undefined}
                height={svgSize.height || undefined}
                style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: svgSize.width ? `${svgSize.width}px` : '100%',
                  height: svgSize.height ? `${svgSize.height}px` : '100%',
                  pointerEvents: 'none',
                  zIndex: 1
                }}
                shapeRendering="crispEdges"
              >
                {svgLines.map((line, i) => (
                  <line
                    key={i}
                    x1={line.x1}
                    y1={line.y1}
                    x2={line.x2}
                    y2={line.y2}
                    stroke="#888"
                    strokeWidth={1.5}
                  />
                ))}
              </svg>

              {/* Nodes rendered in the same wrapper used for measurements */}
              <div
                className="w-max"
                style={{
                  minHeight: 60,
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  position: 'relative',
                  zIndex: 2
                }}
              >
                <FloatingTimelineTree
                  node={timelineRoot}
                  currentNode={currentNode}
                  onJump={handleJumpToNode}
                  nodeRefs={nodeRefs}
                />
              </div>
            </div>
          </div>
        )}

        {/* Scene Background & Main Text */}
        <div className="flex-1 relative overflow-hidden flex flex-col items-center justify-center">
          <SceneBackground scene={gameState.sceneBackground} mood={gameState.mood} />
          <div className="relative z-10 flex flex-col items-center justify-center h-full">
            {/* Main text */}
            <div id="main-chat-container" className="flex flex-col items-center mb-8 px-4">
              <div className="bg-background/80 rounded-xl p-6 shadow-xl relative">
                {/* Show streaming content or thinking indicator */}
                {isStreamingResponse && !streamingContent ? (
                  <div className="flex items-center gap-2 text-muted-foreground">
                    <div className="animate-pulse">●</div>
                    <span>AI is thinking...</span>
                  </div>
                ) : null}
                
                {/* Show the AI response content */}
                {(currentNode.message || streamingContent) && (
                  <div className="space-y-4">
                    {/* Show user message above AI response for non-root messages */}
                    {currentNode.parent && currentNode.parent.sender === 'user' && (
                      <div className="bg-muted/30 rounded-lg p-4 border-l-4 border-accent">
                        <div className="flex items-center gap-2 mb-2">
                          <span className="text-sm font-medium text-muted-foreground">You:</span>
                        </div>
                        <div className="text-foreground">
                          {currentNode.parent.message}
                        </div>
                      </div>
                    )}
                    
                    {/* AI response content */}
                    <div>
                      {currentNode.parent && currentNode.parent.sender === 'user' && (
                        <div className="flex items-center gap-2 mb-2">
                          <span className="text-sm font-medium text-muted-foreground">
                            {parameters?.settingType === 'character' && parameters.characterSetting 
                              ? parameters.characterSetting.name 
                              : parameters?.settingType === 'world' && parameters.worldSetting 
                                ? parameters.worldSetting.name 
                                : 'AI'}:
                          </span>
                        </div>
                      )}
                      <ReactMarkdown 
                        remarkPlugins={[remarkGfm]}
                        components={{
                          // Custom styling for markdown elements
                          h1: ({children}) => <h1 className="text-2xl font-bold mb-4 text-foreground">{children}</h1>,
                          h2: ({children}) => <h2 className="text-xl font-bold mb-3 text-foreground">{children}</h2>,
                          h3: ({children}) => <h3 className="text-lg font-bold mb-2 text-foreground">{children}</h3>,
                          p: ({children}) => <p className="mb-3 leading-relaxed">{children}</p>,
                          strong: ({children}) => <strong className="font-bold text-foreground">{children}</strong>,
                          em: ({children}) => <em className="italic">{children}</em>,
                          code: ({children}) => <code className="bg-muted px-1 py-0.5 rounded text-sm font-mono">{children}</code>,
                          pre: ({children}) => <pre className="bg-muted p-3 rounded-lg overflow-x-auto mb-3">{children}</pre>,
                          blockquote: ({children}) => <blockquote className="border-l-4 border-accent pl-4 italic text-muted-foreground mb-3">{children}</blockquote>,
                          ul: ({children}) => <ul className="list-disc list-inside mb-3 space-y-1">{children}</ul>,
                          ol: ({children}) => <ol className="list-decimal list-inside mb-3 space-y-1">{children}</ol>,
                          li: ({children}) => <li className="leading-relaxed">{children}</li>,
                          table: ({children}) => <table className="w-full border-collapse border border-border mb-3">{children}</table>,
                          th: ({children}) => <th className="border border-border px-3 py-2 bg-muted font-bold">{children}</th>,
                          td: ({children}) => <td className="border border-border px-3 py-2">{children}</td>,
                        }}
                      >
                        {streamingContent || currentNode.message}
                      </ReactMarkdown>
                    </div>
                  </div>
                )}
                
                {/* Regenerate button - only show for AI messages, but not the root message */}
                {currentNode.sender === 'ai' && currentNode.parent !== null && (
                  <div className="absolute top-2 right-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleRegenerateResponse}
                      disabled={isTyping || isStreamingResponse}
                      className="h-8 w-8 p-0 hover:bg-accent/10 hover:border-accent transition-all duration-200"
                      title="Create new branch with different AI response"
                    >
                      {isTyping ? (
                        <div className="animate-spin h-4 w-4 border-2 border-current border-t-transparent rounded-full" />
                      ) : (
                        <span className="text-sm">🌿</span>
                      )}
                    </Button>
                  </div>
                )}
              </div>
            </div>
            {/* Choices and input (only if AI node) */}
            {currentNode.sender === 'ai' && (
              <div className="w-full max-w-2xl space-y-4">
                {/* Choice buttons */}
                <ChoiceButtons
                  // key ensures fresh mount when node changes to avoid stale default options
                  key={`choices-${currentNode.id}-${(currentNode.choices || []).length}-${additionalOptions.length}`}
                  choices={[...(currentNode.choices || []), ...additionalOptions]}
                  character={character}
                  onChoiceSelect={handleChoiceSelect}
                  disabled={isTyping || isStreamingResponse}
                />

                {/* Enhanced Generate More Options Button */}
                {!isStreamingResponse && (
                  <div className="flex justify-center pt-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleGenerateMoreOptions}
                      disabled={isGeneratingOptions || isTyping}
                      className="text-sm px-6 py-2 rounded-lg border-2 hover:border-accent hover:bg-accent/5 transition-all duration-200"
                    >
                      {isGeneratingOptions ? (
                        <div className="flex items-center gap-2">
                          <div className="animate-spin h-4 w-4 border-2 border-current border-t-transparent rounded-full" />
                          <span>Generating new options...</span>
                        </div>
                      ) : (
                        <div className="flex items-center gap-2">
                          <span className="text-lg">🎲</span>
                          <span>Generate More Options</span>
                        </div>
                      )}
                    </Button>
                  </div>
                )}

                {/* Enhanced Custom Message Input */}
                <div className="space-y-4 border-t border-border pt-6">
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <span className="font-medium">💬 Write your own response</span>
                  </div>
                  <div className="flex gap-3">
                    <div className="flex-1 relative">
                      <textarea
                        value={messageInput}
                        onChange={(e) => {
                          console.log('[DEBUG] textarea onChange', { prev: messageInput, next: e.target.value });
                          setMessageInput(e.target.value);
                        }}
                        placeholder="Share your thoughts, ask questions, or take action..."
                        className="w-full min-h-[100px] p-4 rounded-xl border border-border bg-background/50 text-foreground resize-none focus:outline-none focus:ring-2 focus:ring-accent focus:border-accent transition-all duration-200 placeholder:text-muted-foreground/60"
                        disabled={isTyping || isStreamingResponse}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter' && !e.shiftKey) {
                            e.preventDefault();
                            console.log('[DEBUG] textarea Enter submit', { messageInput });
                            handleSubmitMessage();
                          }
                        }}
                        onBlur={() => {
                          console.log('[DEBUG] textarea onBlur', { messageInput });
                        }}
                      />

                    </div>
                    <div className="flex flex-col gap-3">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          console.log('[DEBUG] enhance clicked', { messageInput });
                          handleEnhancePrompt();
                        }}
                        disabled={!messageInput.trim() || isEnhancing || isTyping || isStreamingResponse}
                        title="Enhance your message with AI suggestions"
                        className="h-12 w-12 p-0 hover:bg-accent/10 hover:border-accent transition-all duration-200"
                      >
                        {isEnhancing ? (
                          <div className="animate-spin h-5 w-5 border-2 border-current border-t-transparent rounded-full" />
                        ) : (
                          <span className="text-lg">✨</span>
                        )}
                      </Button>
                      <Button
                        onClick={() => {
                          console.log('[DEBUG] send clicked', { messageInput });
                          handleSubmitMessage();
                        }}
                        disabled={!messageInput.trim() || isTyping || isStreamingResponse}
                        size="sm"
                        className="h-12 px-4 bg-gradient-to-r from-accent to-accent/80 hover:from-accent/90 hover:to-accent/70 transition-all duration-200 shadow-lg hover:shadow-xl"
                      >
                        {isTyping ? (
                          <div className="flex items-center gap-2">
                            <div className="animate-spin h-4 w-4 border-2 border-current border-t-transparent rounded-full" />
                            <span>Sending...</span>
                          </div>
                        ) : (
                          <span className="font-medium">Send Message</span>
                        )}
                      </Button>
                    </div>
                  </div>
                  {messageInput.trim() && (
                    <div className="text-xs text-muted-foreground bg-muted/30 rounded-lg p-2">
                      💡 Tip: Press Enter to send, Shift+Enter for new line
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Persona Images in Bottom Corners */}
      {parameters?.userPersona && (
        <PersonaImage
          image={parameters.userPersona.image}
          name={parameters.userPersona.name}
          position="bottom-left"
        />
      )}
      
      {/* AI Character Image */}
      {parameters?.settingType === 'character' && parameters.characterSetting && (
        <PersonaImage
          image={parameters.characterSetting.image_url}
          name={parameters.characterSetting.name}
          position="bottom-right"
        />
      )}
    </div>
  );
};

export default RPGChat;