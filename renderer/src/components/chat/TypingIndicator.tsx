import React from 'react';
import { Avatar, AvatarFallback } from '../ui/avatar';

export const TypingIndicator: React.FC = () => {
  return (
    <div className="flex gap-3 max-w-4xl animate-message-slide">
      <Avatar className="w-10 h-10 shadow-character border-2 border-character-ai">
        <AvatarFallback className="bg-character-ai/20 text-lg">
          📖
        </AvatarFallback>
      </Avatar>

      <div className="flex flex-col gap-1">
        <div className="text-sm text-muted-foreground">
          <span className="font-medium">Narrator</span>
          <span className="text-xs opacity-75 ml-2">is thinking...</span>
        </div>
        
        <div className="bg-gradient-card text-card-foreground rounded-2xl px-4 py-3 mr-12 shadow-lg">
          <div className="flex gap-1">
            <div className="w-2 h-2 bg-current rounded-full animate-typing" style={{ animationDelay: '0ms' }}></div>
            <div className="w-2 h-2 bg-current rounded-full animate-typing" style={{ animationDelay: '200ms' }}></div>
            <div className="w-2 h-2 bg-current rounded-full animate-typing" style={{ animationDelay: '400ms' }}></div>
          </div>
        </div>
      </div>
    </div>
  );
};