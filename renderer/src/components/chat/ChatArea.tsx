import React, { forwardRef } from 'react';
import { ChatMessage } from '../RPGChat';
import { MessageBubble } from './MessageBubble';
import { TypingIndicator } from './TypingIndicator';
import { cn } from '../../lib/utils';

interface ChatAreaProps {
  messages: ChatMessage[];
  isTyping: boolean;
  className?: string;
}

export const ChatArea = forwardRef<HTMLDivElement, ChatAreaProps>(
  ({ messages, isTyping, className }, ref) => {
    return (
      <div 
        ref={ref}
        className={cn(
          "flex-1 overflow-y-auto p-4 space-y-4 scroll-smooth",
          className
        )}
      >
        {messages.map((message) => (
          <MessageBubble 
            key={message.id} 
            message={message}
            className="animate-message-slide"
          />
        ))}
        
        {isTyping && <TypingIndicator />}
      </div>
    );
  }
);

ChatArea.displayName = 'ChatArea';