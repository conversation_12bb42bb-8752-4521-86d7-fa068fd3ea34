import React from 'react';
import { ChatMessage } from '../RPGChat';
import { Avatar, AvatarFallback } from '../ui/avatar';
import { cn } from '../../lib/utils';

interface MessageBubbleProps {
  message: ChatMessage;
  className?: string;
}

export const MessageBubble: React.FC<MessageBubbleProps> = ({ message, className }) => {
  const isUser = message.sender === 'user';
  
  const getEmotionIcon = (emotion?: string) => {
    switch (emotion) {
      case 'happy': return '😊';
      case 'angry': return '😠';
      case 'sad': return '😢';
      case 'mysterious': return '🔮';
      default: return '🎭';
    }
  };

  const getCharacterAvatar = (characterName?: string) => {
    const avatarMap: Record<string, string> = {
      'Narrator': '📖',
      '<PERSON>ara the Wise': '🧙‍♀️',
      'Grimjaw the Bandit': '🗡️',
      'Village Elder': '👴',
      'Mysterious Stranger': '🎭'
    };
    return avatarMap[characterName || ''] || '🎮';
  };

  return (
    <div className={cn("flex gap-3 max-w-4xl", isUser ? "ml-auto flex-row-reverse" : "", className)}>
      <Avatar className={cn(
        "w-10 h-10 shadow-character border-2",
        isUser ? "border-character-user" : "border-character-ai"
      )}>
        <AvatarFallback className={cn(
          "text-lg",
          isUser ? "bg-character-user/20" : "bg-character-ai/20"
        )}>
          {isUser ? '👤' : getCharacterAvatar(message.characterName)}
        </AvatarFallback>
      </Avatar>

      <div className={cn(
        "flex flex-col gap-1",
        isUser ? "items-end" : "items-start"
      )}>
        {!isUser && message.characterName && (
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <span className="font-medium">{message.characterName}</span>
            {message.emotion && (
              <span className="text-xs opacity-75">
                {getEmotionIcon(message.emotion)}
              </span>
            )}
          </div>
        )}
        
        <div className={cn(
          "rounded-2xl px-4 py-3 max-w-lg shadow-lg transition-all duration-300 hover:shadow-xl",
          isUser 
            ? "bg-gradient-primary text-primary-foreground ml-12" 
            : "bg-gradient-card text-card-foreground mr-12"
        )}>
          <p className="leading-relaxed whitespace-pre-wrap">{message.content}</p>
        </div>

        <span className="text-xs text-muted-foreground px-2">
          {new Date(message.timestamp).toLocaleTimeString([], { 
            hour: '2-digit', 
            minute: '2-digit' 
          })}
        </span>
      </div>
    </div>
  );
};