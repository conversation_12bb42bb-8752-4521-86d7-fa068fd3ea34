import React, { useState, useEffect } from 'react';
import RPGChat from './RPGChat';
import ChatSetup from './ChatSetup';
import ApiKeySetup from './ApiKeySetup';
import SystemPromptSettings from './SystemPromptSettings';
import PersonaCreation from './CharacterCreation';
import PersonaManagement from './PersonaManagement';
import DatabaseViewer from './DatabaseViewer';
import { Button } from './ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Settings, Plus, UserPlus, Database } from 'lucide-react';

const Directory = () => {
  const [stage, setStage] = useState('directory'); // 'directory', 'apiKey', 'systemPrompts', 'setup', 'character', 'personaManagement', 'chat', 'database'
  const [currentChat, setCurrentChat] = useState(null);
  const [chatSetupKey, setChatSetupKey] = useState(0); // Key to force ChatSetup re-render
  const [loading, setLoading] = useState(false);
  const [editingCharacter, setEditingCharacter] = useState(null); // For CharacterCreation
  const [recentChats, setRecentChats] = useState([]);
  const [characters, setCharacters] = useState([]);

  // Load recent chats data
  const loadData = React.useCallback(async () => {
    try {
      setLoading(true);
      const [chatsResult, charactersResult] = await Promise.all([
        window.electronAPI.getDirectoryChats(10),
        window.electronAPI.getCharacterSettings()
      ]);
      
      if (chatsResult.success) {
        setRecentChats(chatsResult.chats || []);
      }
      
      if (charactersResult.success) {
        setCharacters(charactersResult.settings || []);
      }
    } catch (error) {
      console.error('Error loading directory data:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  // Initial load
  useEffect(() => {
    loadData();
  }, [loadData]);

  // Reload whenever we return to the directory stage
  useEffect(() => {
    if (stage === 'directory') {
      loadData();
    }
  }, [stage, loadData]);

  // Optional: refresh on window focus (helps when DB updates happen while away)
  useEffect(() => {
    const onFocus = () => {
      if (stage === 'directory') {
        loadData();
      }
    };
    window.addEventListener('focus', onFocus);
    return () => window.removeEventListener('focus', onFocus);
  }, [stage, loadData]);

  const reset = () => {
    setStage('directory');
    setEditingCharacter(null);
  };





  const newChatFlow = async () => {
    const { success, apiKey } = await window.electronAPI.getActiveApiKey().catch(() => ({}));
    setStage(success && apiKey ? 'setup' : 'apiKey');
  };

  const handleCharacterCreationComplete = () => {
    setStage('directory');
    setEditingCharacter(null);
  };

  // Utility function to reconstruct timeline from database chats
  const reconstructTimeline = (chats) => {
    console.log('🔄 Reconstructing timeline from', chats.length, 'chats');

    if (chats.length === 0) return null;

    // Sort chats by creation time to ensure proper order
    const sortedChats = [...chats].sort((a, b) => new Date(a.created_at) - new Date(b.created_at));

    // Create a map of chat ID to timeline node (both user and AI)
    const nodeMap = new Map();

    // First pass: create timeline nodes from database chats
    sortedChats.forEach(chat => {
      try {
        const contents = JSON.parse(chat.contents);

        // Determine sender based on content structure
        const isUserMessage = contents.sender === 'user' || contents.isUserInput === true;

        const node = {
          id: `chat-${chat.id}`,
          message: contents.content || 'No content',
          sender: isUserMessage ? 'user' : 'ai',
          choices: isUserMessage ? [] : (contents.options || []).map((option, index) => ({
            id: `choice-${chat.id}-${index}`,
            text: option,
            preview: option.toLowerCase()
          })),
          children: [],
          parent: null,
          chatId: chat.id,
          parentChatId: chat.parent_id
        };

        // Add user-specific properties if it's a user message
        if (isUserMessage) {
          node.userInput = contents.content;
        }

        nodeMap.set(chat.id, node);
        console.log(`📝 Created ${isUserMessage ? 'USER' : 'AI'} node for chat ${chat.id}:`, contents.content?.substring(0, 50));
      } catch (error) {
        console.error('Error parsing chat contents:', error, chat);
      }
    });

    // Second pass: establish parent-child relationships between all nodes
    // This creates the proper conversation tree with user and AI nodes
    let rootNode = null;
    nodeMap.forEach(node => {
      if (node.parentChatId) {
        const parentNode = nodeMap.get(node.parentChatId);
        if (parentNode) {
          node.parent = parentNode;
          parentNode.children.push(node);
        }
      } else {
        // This is a root node (should be AI)
        rootNode = node;
      }
    });

    // If we have multiple root nodes, find the earliest one
    if (!rootNode) {
      const rootNodes = Array.from(nodeMap.values()).filter(node => !node.parentChatId);
      if (rootNodes.length > 0) {
        rootNode = rootNodes[0]; // Take the first one (already sorted by creation time)
      }
    }

    console.log('✅ Timeline reconstruction complete, root node:', rootNode);
    console.log('📊 Node map:', Array.from(nodeMap.values()));

    // Find the most recent chat ID for continuing the conversation
    const mostRecentChatId = sortedChats.length > 0 ? sortedChats[sortedChats.length - 1].id : null;
    console.log('🕒 Most recent chat ID for continuation:', mostRecentChatId);

    return { rootNode, mostRecentChatId };
  };

  const handleChatClick = async (chat) => {
    try {
      console.log('🎯 Loading chat:', chat);

      // Get the full conversation tree
      const conversationResult = await window.electronAPI.getConversationTree(chat.id);
      if (!conversationResult.success) {
        console.error('Failed to load conversation tree:', conversationResult.error);
        return;
      }

      const { conversationData } = conversationResult;
      console.log('📚 Loaded conversation data:', conversationData);

      // Reconstruct the timeline from the chat data
      const timelineResult = reconstructTimeline(conversationData.allChats);
      const fullTimeline = timelineResult?.rootNode;
      const mostRecentChatId = timelineResult?.mostRecentChatId;

      // Load the character or world setting based on the chat
      if (chat.setting_type === 'character' && chat.character_id) {
        const characterResult = await window.electronAPI.getCharacterSetting(chat.character_id);
        console.log('📥 Character setting result:', characterResult);

        if (characterResult.success) {
          const characterSetting = characterResult.setting; // Fix: use 'setting' not 'characterSetting'
          console.log('👤 Loaded character setting:', characterSetting);

          // Load user persona if available
          let userPersona = null;
          if (chat.user_persona_id) {
            const personaResult = await window.electronAPI.getPersona(chat.user_persona_id);
            if (personaResult.success) {
              userPersona = personaResult.persona;
            }
          }

          console.log('🔄 Loading existing character chat with parameters:', {
            settingType: 'character',
            characterSetting,
            userPersona,
            chatId: chat.id,
            currentChatId: mostRecentChatId // Use most recent chat ID for branching
          });

          const parameters = {
            settingType: 'character',
            characterSetting,
            userPersona,
            fullTimeline, // Pass the reconstructed timeline
            existingChat: chat, // Also pass the original chat for reference
            currentChatId: mostRecentChatId // Pass the most recent chat ID for proper branching
          };

          setCurrentChat({ isNew: false, params: parameters });
          setStage('chat');
        }
      } else if (chat.setting_type === 'world' && chat.setting_id) {
        const worldResult = await window.electronAPI.getWorldSetting(chat.setting_id);
        console.log('📥 World setting result:', worldResult);

        if (worldResult.success) {
          const worldSetting = worldResult.setting; // Fix: use 'setting' not 'worldSetting'
          console.log('🌍 Loaded world setting:', worldSetting);

          // Load user persona if available
          let userPersona = null;
          if (chat.user_persona_id) {
            const personaResult = await window.electronAPI.getPersona(chat.user_persona_id);
            if (personaResult.success) {
              userPersona = personaResult.persona;
            }
          }

          console.log('🔄 Loading existing world chat with parameters:', {
            settingType: 'world',
            worldSetting,
            userPersona,
            chatId: chat.id,
            currentChatId: mostRecentChatId // Use most recent chat ID for branching
          });

          const parameters = {
            settingType: 'world',
            worldSetting,
            userPersona,
            fullTimeline, // Pass the reconstructed timeline
            existingChat: chat, // Also pass the original chat for reference
            currentChatId: mostRecentChatId // Pass the most recent chat ID for proper branching
          };

          setCurrentChat({ isNew: false, params: parameters });
          setStage('chat');
        }
      }
    } catch (error) {
      console.error('Error loading chat:', error);
    }
  };

  const handleNewChatWithCharacter = async (chat, event) => {
    // Prevent the main chat click from firing
    event.stopPropagation();

    try {
      console.log('🆕 Starting new chat with character from:', chat);

      // Load the character/world setting to pre-fill the setup
      let characterSetting = null;
      let worldSetting = null;

      if (chat.setting_type === 'character' && chat.character_id) {
        const charResult = await window.electronAPI.getCharacterSetting(chat.character_id);
        if (charResult.success) {
          characterSetting = charResult.setting; // Fix: use 'setting' not 'characterSetting'
        }
      } else if (chat.setting_type === 'world' && chat.setting_id) {
        const worldResult = await window.electronAPI.getWorldSetting(chat.setting_id);
        if (worldResult.success) {
          worldSetting = worldResult.setting; // Fix: use 'setting' not 'worldSetting'
        }
      }

      // Load user persona to pre-select it
      let userPersona = null;
      if (chat.user_persona_id) {
        const personaResult = await window.electronAPI.getPersona(chat.user_persona_id);
        if (personaResult.success) {
          userPersona = personaResult.persona;
        }
      }

      // Set the editing character for pre-filling the setup
      const editingData = {
        settingType: chat.setting_type,
        characterSetting,
        worldSetting,
        userPersona
      };

      console.log('🎯 Setting editingCharacter data:', editingData);
      setEditingCharacter(editingData);

      // Navigate to setup with pre-filled data
      setStage('setup');
    } catch (error) {
      console.error('Error preparing new chat with character:', error);
    }
  };

  const handleCharacterClick = async (character) => {
    try {
      console.log('🎭 Starting new chat with character:', character);

      // Set the editing character for pre-filling the setup
      // Use the legacy structure so it goes to character-selection stage for persona selection
      const editingData = {
        id: character.id,
        name: character.name,
        personality: character.personality,
        appearance: character.appearance,
        image: character.image_url,
        created_at: character.created_at,
        character: character.character,
        scenario: character.scenario,
        initialMessage: character.initial_message,
        exampleDialogs: character.example_dialogs
      };

      console.log('🎯 Setting editingCharacter data:', editingData);
      setEditingCharacter(editingData);

      // Navigate to setup with pre-filled data
      setStage('setup');
    } catch (error) {
      console.error('Error preparing new chat with character:', error);
    }
  };






 





  // Render based on current stage
  if (stage === 'apiKey') return <ApiKeySetup onApiKeySet={() => setStage('setup')} onBack={reset} onShowSystemPrompts={() => setStage('systemPrompts')} />;
  if (stage === 'systemPrompts') return <SystemPromptSettings onBack={() => setStage('apiKey')} />;
  if (stage === 'character') return <PersonaCreation onBack={reset} onPersonaCreated={handleCharacterCreationComplete} editingPersona={editingCharacter} />;
  if (stage === 'personaManagement') return <PersonaManagement onBack={reset} />;
  if (stage === 'database') return <DatabaseViewer onBack={reset} />;
  if (stage === 'setup') return (
    <ChatSetup
      key={chatSetupKey}
      onStartChat={(params) => { setCurrentChat({ isNew: true, params }); setStage('chat'); }}
      onBack={reset}
      onNavigateToCharacterCreation={() => setStage('character')}
      editingCharacter={editingCharacter}
    />
  );
  if (stage === 'chat') return (
    <RPGChat
      parameters={currentChat.params}
      onBackToDirectory={reset}
    />
  );

  // Directory view
  return (
    <div className="min-h-screen bg-black text-white p-6">
      <header className="flex justify-between items-center mb-8">
        <div className="flex items-center gap-3">
          <img 
            src="/assets/Icon.png" 
            alt="Electrobox RPG Logo" 
            className="w-10 h-10 rounded-lg"
          />
          <h1 className="text-2xl font-bold text-blue-400">Electrobox RPG</h1>
        </div>
        <div className="flex gap-2">
          <Button
            size="sm"
            variant="outline"
            onClick={() => setStage('database')}
            className="border-green-500 text-green-400 hover:bg-green-500 hover:text-white"
          >
            <Database className="w-4 h-4 mr-2" /> Database
          </Button>
          <Button 
            size="sm" 
            variant="outline" 
            onClick={() => setStage('personaManagement')}
            className="border-blue-500 text-blue-400 hover:bg-blue-500 hover:text-white"
          >
            <UserPlus className="w-4 h-4 mr-2" /> Edit Personas
          </Button>
          <Button 
            size="sm" 
            variant="outline" 
            onClick={() => setStage('apiKey')}
            className="border-blue-500 text-blue-400 hover:bg-blue-500 hover:text-white"
          >
            <Settings className="w-4 h-4 mr-2" /> API Key
          </Button>
        </div>
      </header>

      <main className="max-w-6xl mx-auto">
        {/* New Chat Section - Always visible */}
        <div className="mb-8 text-center">
          <h1 className="text-2xl font-bold text-white mb-2">Welcome to Electrobox RPG!</h1>
          <p className="text-gray-400 mb-6">Create new characters and worlds, or continue your existing adventures</p>
          <Button
            size="lg"
            onClick={newChatFlow}
            className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 text-lg font-semibold"
          >
            <Plus className="w-5 h-5 mr-2" /> Start New Adventure
          </Button>
        </div>

        {/* Recent Chats Section */}
        {recentChats.length > 0 && (
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-white mb-4">Recent Adventures</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {recentChats.map((chat) => (
                <Card
                  key={chat.id}
                  className="cursor-pointer hover:shadow-lg transition-all duration-200 bg-gray-800 border-gray-700"
                  onClick={(e) => handleNewChatWithCharacter(chat, e)}
                >
                  <CardContent className="p-4">
                    {/* Title at top */}
                    <h3 className="font-semibold text-white text-lg mb-3 text-center">
                      {chat.character_name || chat.world_name || 'Unknown'}
                    </h3>

                    <div className="flex space-x-4">
                      {/* Character Image on left */}
                      <div className="flex-shrink-0">
                        {chat.character_image_url ? (
                          <img
                            src={chat.character_image_url}
                            alt={chat.character_name || 'Character'}
                            className="w-20 h-20 rounded-lg object-cover border-2 border-gray-600"
                          />
                        ) : (
                          <div className="w-20 h-20 rounded-lg bg-gray-700 border-2 border-gray-600 flex items-center justify-center">
                            <span className="text-gray-400 text-xs">No Image</span>
                          </div>
                        )}
                      </div>

                      {/* Description and info on right */}
                      <div className="flex-1 min-w-0">
                        {/* Description that gets cut off if too long */}
                        <p className="text-gray-300 text-sm mb-3 line-clamp-3">
                          {chat.setting_type === 'character' 
                            ? (chat.character_description || 'No description available for this character.')
                            : (chat.world_description || 'No description available for this world.')
                          }
                        </p>

                        {/* Last chatted with and message count */}
                        <div className="flex items-center justify-between text-xs text-gray-500 mb-3">
                          <div className="flex items-center">
                            <span className="mr-1">🕒</span>
                            <span>
                              {new Date(chat.updated_at || chat.created_at).toLocaleDateString()}
                            </span>
                          </div>
                          <div className="flex items-center">
                            <span className="mr-1">💬</span>
                            <span>{chat.message_count || 0} messages</span>
                          </div>
                        </div>

                        {/* Continue chat button */}
                        <Button
                          size="sm"
                          className="w-full bg-purple-600 hover:bg-purple-700 text-white"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleChatClick(chat);
                          }}
                        >
                          Continue
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )}

        {/* Welcome Message when no chats */}
        {recentChats.length === 0 && !loading && (
          <div className="text-center py-12">
            <div className="bg-gray-800 rounded-lg p-8 border border-gray-700">
              <p className="text-gray-400 text-lg">No adventures yet!</p>
              <p className="text-gray-500 text-sm mt-2">Click "Start New Adventure" above to create your first character and begin your journey.</p>
            </div>
          </div>
        )}

        {/* My Characters Section */}
        {characters.length > 0 && (
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-white mb-4">My Characters</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {characters.map((character) => (
                <Card
                  key={character.id}
                  className="cursor-pointer hover:shadow-lg transition-all duration-200 bg-gray-800 border-gray-700"
                  onClick={() => handleCharacterClick(character)}
                >
                  <CardContent className="p-4">
                    {/* Character Name at top */}
                    <h3 className="font-semibold text-white text-lg mb-3 text-center">
                      {character.name || 'Unnamed Character'}
                    </h3>

                    <div className="flex space-x-4">
                      {/* Character Image on left */}
                      <div className="flex-shrink-0">
                        {character.image_url ? (
                          <img
                            src={character.image_url}
                            alt={character.name || 'Character'}
                            className="w-20 h-20 rounded-lg object-cover border-2 border-gray-600"
                          />
                        ) : (
                          <div className="w-20 h-20 rounded-lg bg-gray-700 border-2 border-gray-600 flex items-center justify-center">
                            <span className="text-gray-400 text-xs">No Image</span>
                          </div>
                        )}
                      </div>

                      {/* Scenario on right */}
                      <div className="flex-1 min-w-0">
                        <p className="text-gray-300 text-sm line-clamp-4">
                          {character.scenario || 'No scenario description available.'}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )}

        {/* Loading State */}
        {loading && (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-400 mx-auto"></div>
            <p className="text-gray-400 mt-4">Loading your adventures...</p>
          </div>
        )}
      </main>
    </div>
  );
};

export default Directory;
