import React, { useState, useEffect } from 'react';
import { Button } from './ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from './ui/table';
import { Badge } from './ui/badge';
import { ArrowLeft, Database, Table as TableIcon, Eye, ChevronLeft, ChevronRight, Trash2 } from 'lucide-react';
import { Alert, AlertDescription } from './ui/alert';

interface DatabaseTable {
  name: string;
}

interface TableColumn {
  cid: number;
  name: string;
  type: string;
  notnull: number;
  dflt_value: any;
  pk: number;
}

interface DatabaseViewerProps {
  onBack: () => void;
}

const DatabaseViewer: React.FC<DatabaseViewerProps> = ({ onBack }) => {
  const [tables, setTables] = useState<DatabaseTable[]>([]);
  const [selectedTable, setSelectedTable] = useState<string | null>(null);
  const [tableSchema, setTableSchema] = useState<TableColumn[]>([]);
  const [tableData, setTableData] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');
  const [currentPage, setCurrentPage] = useState(0);
  const [totalCount, setTotalCount] = useState(0);
  const [pageSize] = useState(50);

  useEffect(() => {
    loadTables();
  }, []);

  const loadTables = async () => {
    try {
      setLoading(true);
      setError('');
      const result = await window.electronAPI.getAllTables();
      if (result.success) {
        setTables(result.tables);
      } else {
        setError(result.error || 'Failed to load tables');
      }
    } catch (err) {
      setError('Failed to load database tables');
      console.error('Error loading tables:', err);
    } finally {
      setLoading(false);
    }
  };

  const loadTableData = async (tableName: string, page: number = 0) => {
    try {
      setLoading(true);
      setError('');
      
      // Load schema
      const schemaResult = await window.electronAPI.getTableSchema(tableName);
      if (!schemaResult.success) {
        throw new Error(schemaResult.error || 'Failed to load table schema');
      }
      
      // Load data
      const dataResult = await window.electronAPI.getTableData(tableName, pageSize, page * pageSize);
      if (!dataResult.success) {
        throw new Error(dataResult.error || 'Failed to load table data');
      }
      
      setTableSchema(schemaResult.schema);
      setTableData(dataResult.data);
      setTotalCount(dataResult.count);
      setSelectedTable(tableName);
      setCurrentPage(page);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load table data');
      console.error('Error loading table data:', err);
    } finally {
      setLoading(false);
    }
  };

  const handlePageChange = (newPage: number) => {
    if (selectedTable) {
      loadTableData(selectedTable, newPage);
    }
  };

  const handleDeleteRow = async (rowId: any, idColumn: string = 'id') => {
    if (!selectedTable) return;
    
    try {
      const result = await window.electronAPI.deleteRow(selectedTable, rowId, idColumn);
      if (result.success) {
        // Reload the current page to reflect the deletion
        loadTableData(selectedTable, currentPage);
      } else {
        setError(result.error || 'Failed to delete row');
      }
    } catch (err) {
      setError('Failed to delete row');
      console.error('Error deleting row:', err);
    }
  };

  const formatCellValue = (value: any, columnName: string, columnType: string) => {
    if (value === null || value === undefined) {
      return <span className="text-gray-500 italic">NULL</span>;
    }

    // Handle image data - check if it's base64 encoded image
    if (typeof value === 'string' && (
      columnName.toLowerCase().includes('image') || 
      columnName.toLowerCase().includes('img') ||
      value.startsWith('data:image/') ||
      (value.length > 100 && /^[A-Za-z0-9+/=]+$/.test(value))
    )) {
      const imageData = value.startsWith('data:image/') ? value : `data:image/jpeg;base64,${value}`;
      return (
        <div className="flex items-center gap-2">
          <img 
            src={imageData} 
            alt="Database image" 
            className="w-12 h-12 object-cover rounded border"
            onError={(e) => {
              // If image fails to load, show as text
              (e.currentTarget as HTMLImageElement).style.display = 'none';
              (e.currentTarget.nextElementSibling as HTMLElement)!.style.display = 'block';
            }}
          />
          <span className="text-xs text-gray-500 hidden">
            {value.length > 50 ? `${value.substring(0, 50)}...` : value}
          </span>
        </div>
      );
    }

    // Handle long text
    if (typeof value === 'string' && value.length > 100) {
      return (
        <div className="max-w-xs">
          <span className="text-sm">{value.substring(0, 100)}...</span>
          <Badge variant="secondary" className="ml-2 text-xs">
            {value.length} chars
          </Badge>
        </div>
      );
    }

    // Handle JSON
    if (typeof value === 'string' && (value.startsWith('{') || value.startsWith('['))) {
      try {
        JSON.parse(value);
        return (
          <div className="max-w-xs">
            <code className="text-xs bg-gray-100 dark:bg-gray-800 p-1 rounded">
              {value.length > 50 ? `${value.substring(0, 50)}...` : value}
            </code>
            <Badge variant="secondary" className="ml-2 text-xs">JSON</Badge>
          </div>
        );
      } catch {
        // Not valid JSON, treat as regular string
      }
    }

    return <span className="text-sm">{String(value)}</span>;
  };

  const totalPages = Math.ceil(totalCount / pageSize);

  if (loading && tables.length === 0) {
    return (
      <div className="min-h-screen bg-black text-white p-6">
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-400 mx-auto"></div>
          <p className="text-gray-400 mt-4">Loading database...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-black text-white p-6">
      <header className="flex justify-between items-center mb-8">
        <div className="flex items-center gap-3">
          <Button 
            variant="outline" 
            size="sm" 
            onClick={onBack}
            className="border-gray-600 text-gray-300 hover:bg-gray-700"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back
          </Button>
          <Database className="w-8 h-8 text-blue-400" />
          <h1 className="text-2xl font-bold text-blue-400">Database Viewer</h1>
        </div>
      </header>

      {error && (
        <Alert className="mb-6 border-red-500 bg-red-900/20">
          <AlertDescription className="text-red-300">{error}</AlertDescription>
        </Alert>
      )}

      <main className="max-w-7xl mx-auto">
        {!selectedTable ? (
          // Tables list view
          <Card className="bg-gray-900 border-gray-800">
            <CardHeader>
              <CardTitle className="text-blue-400 flex items-center gap-2">
                <TableIcon className="w-5 h-5" />
                Database Tables
              </CardTitle>
              <CardDescription className="text-gray-400">
                Click on a table to view its data
              </CardDescription>
            </CardHeader>
            <CardContent>
              {tables.length === 0 ? (
                <p className="text-gray-400 text-center py-8">No tables found in database</p>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {tables.map((table) => (
                    <Card 
                      key={table.name}
                      className="bg-gray-800 border-gray-700 hover:bg-gray-700 hover:border-blue-500 cursor-pointer transition-all duration-200"
                      onClick={() => loadTableData(table.name)}
                    >
                      <CardContent className="p-4">
                        <div className="flex items-center gap-2">
                          <TableIcon className="w-4 h-4 text-blue-400" />
                          <span className="font-medium text-white">{table.name}</span>
                        </div>
                        <Button 
                          size="sm" 
                          className="mt-2 w-full bg-blue-600 hover:bg-blue-700"
                        >
                          <Eye className="w-4 h-4 mr-2" />
                          View Data
                        </Button>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        ) : (
          // Table data view
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={() => setSelectedTable(null)}
                  className="border-gray-600 text-gray-300 hover:bg-gray-700"
                >
                  <ChevronLeft className="w-4 h-4 mr-2" />
                  Back to Tables
                </Button>
                <h2 className="text-xl font-bold text-blue-400">{selectedTable}</h2>
                <Badge variant="secondary" className="text-xs">
                  {totalCount} rows
                </Badge>
              </div>
              
              {totalPages > 1 && (
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 0}
                    className="border-gray-600 text-gray-300 hover:bg-gray-700"
                  >
                    <ChevronLeft className="w-4 h-4" />
                  </Button>
                  <span className="text-sm text-gray-400">
                    Page {currentPage + 1} of {totalPages}
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage >= totalPages - 1}
                    className="border-gray-600 text-gray-300 hover:bg-gray-700"
                  >
                    <ChevronRight className="w-4 h-4" />
                  </Button>
                </div>
              )}
            </div>

            <Card className="bg-gray-900 border-gray-800">
              <CardContent className="p-0">
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow className="border-gray-700">
                        {tableSchema.map((column) => (
                          <TableHead key={column.name} className="text-blue-400 font-semibold">
                            <div className="flex flex-col">
                              <span>{column.name}</span>
                              <span className="text-xs text-gray-500 font-normal">
                                {column.type}
                                {column.pk ? ' (PK)' : ''}
                                {column.notnull ? ' NOT NULL' : ''}
                              </span>
                            </div>
                          </TableHead>
                        ))}
                        <TableHead className="text-blue-400 font-semibold w-20">
                          Actions
                        </TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {loading ? (
                        <TableRow>
                          <TableCell colSpan={tableSchema.length + 1} className="text-center py-8">
                            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-400 mx-auto"></div>
                            <p className="text-gray-400 mt-2">Loading data...</p>
                          </TableCell>
                        </TableRow>
                      ) : tableData.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={tableSchema.length + 1} className="text-center py-8 text-gray-400">
                            No data found in this table
                          </TableCell>
                        </TableRow>
                      ) : (
                        tableData.map((row, index) => (
                          <TableRow key={index} className="border-gray-700 hover:bg-gray-800">
                            {tableSchema.map((column) => (
                              <TableCell key={column.name} className="text-gray-300">
                                {formatCellValue(row[column.name], column.name, column.type)}
                              </TableCell>
                            ))}
                            <TableCell className="text-gray-300">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation(); // Prevent row click
                                  handleDeleteRow(row[tableSchema.find(col => col.pk)?.name || 'id'], tableSchema.find(col => col.pk)?.name || 'id');
                                }}
                                className="text-red-400 hover:text-red-300"
                              >
                                <Trash2 className="w-4 h-4" />
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </main>
    </div>
  );
};

export default DatabaseViewer;
