import React, { useState, useEffect } from 'react';
import { But<PERSON> } from './ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { ArrowLeft, User, UserPlus, Edit, Trash2 } from 'lucide-react';
import CharacterCreation from './CharacterCreation';
import type { UserPersona } from './ChatSetup';

interface PersonaManagementProps {
  onBack: () => void;
}

const PersonaManagement: React.FC<PersonaManagementProps> = ({ onBack }) => {
  const [userPersonas, setUserPersonas] = useState<UserPersona[]>([]);
  const [showCharacterCreation, setShowCharacterCreation] = useState(false);
  const [editingPersona, setEditingPersona] = useState<UserPersona | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Load user personas on component mount
  useEffect(() => {
    loadUserPersonas();
  }, []);

  const loadUserPersonas = async () => {
    try {
      setIsLoading(true);
      const result = await window.electronAPI.getPersonas();
      if (result.success) {
        setUserPersonas(result.personas || []);
      }
    } catch (error) {
      console.error('Error loading user personas:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle editing persona
  const handleEditPersona = (persona: UserPersona) => {
    setEditingPersona(persona);
    setShowCharacterCreation(true);
  };

  // Handle deleting persona
  const handleDeletePersona = async (persona: UserPersona) => {
    if (window.confirm(`Are you sure you want to delete "${persona.name}"? This action cannot be undone.`)) {
      try {
        const result = await window.electronAPI.deletePersona(persona.id);
        if (result.success) {
          await loadUserPersonas(); // Reload the list
        } else {
          alert('Failed to delete persona. Please try again.');
        }
      } catch (error) {
        console.error('Error deleting persona:', error);
        alert('Error deleting persona. Please try again.');
      }
    }
  };

  // Handle creating new character
  const handleCreateNewCharacter = () => {
    setEditingPersona(null); // Clear any editing state
    setShowCharacterCreation(true);
  };

  // Handle character creation/editing completion
  const handleCharacterCreationComplete = () => {
    setShowCharacterCreation(false);
    setEditingPersona(null);
    loadUserPersonas(); // Reload personas to get updated data
  };

  // Show character creation/editing screen
  if (showCharacterCreation) {
    return (
      <CharacterCreation 
        onBack={handleCharacterCreationComplete}
        onPersonaCreated={handleCharacterCreationComplete}
        editingPersona={editingPersona}
      />
    );
  }

  return (
    <div className="min-h-screen bg-gradient-background p-8">
      <div className="max-w-6xl mx-auto">
        <div className="flex items-center mb-6">
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={onBack} 
            className="mr-4"
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-3xl font-bold text-foreground">Edit Your Personas</h1>
        </div>

        <div className="mb-6">
          <p className="text-muted-foreground mb-4">
            Manage your existing personas or create new ones.
          </p>
        </div>

        {isLoading ? (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-400 mx-auto"></div>
            <p className="text-gray-400 mt-4">Loading your personas...</p>
          </div>
        ) : (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-6">
              {userPersonas.map((persona) => (
                <Card 
                  key={persona.id} 
                  className="hover:shadow-lg transition-all duration-200"
                >
                  <CardHeader className="pb-3">
                    <div className="flex flex-col items-center text-center space-y-3">
                      {persona.image ? (
                        <img 
                          src={persona.image} 
                          alt={persona.name}
                          className="w-20 h-20 rounded-full object-cover border-2 border-gray-200 shadow-md"
                        />
                      ) : (
                        <div className="w-20 h-20 rounded-full bg-gradient-to-br from-blue-100 to-blue-200 flex items-center justify-center border-2 border-blue-300 shadow-md">
                          <User className="w-10 h-10 text-blue-600" />
                        </div>
                      )}
                      <div>
                        <CardTitle className="text-lg font-bold">{persona.name}</CardTitle>
                        <CardDescription className="text-xs">
                          Created {new Date(persona.created_at).toLocaleDateString()}
                        </CardDescription>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <div className="space-y-2">
                      <div>
                        <h4 className="text-sm font-semibold text-gray-700 mb-1">Personality</h4>
                        <p className="text-sm text-muted-foreground line-clamp-3 leading-relaxed">
                          {persona.personality}
                        </p>
                      </div>
                      {persona.appearance && (
                        <div>
                          <h4 className="text-sm font-semibold text-gray-700 mb-1">Appearance</h4>
                          <p className="text-sm text-muted-foreground line-clamp-2 leading-relaxed">
                            {persona.appearance}
                          </p>
                        </div>
                      )}
                      <div className="pt-2 space-y-2">
                        <Button 
                          variant="outline" 
                          size="sm" 
                          onClick={() => handleEditPersona(persona)}
                          className="w-full border-blue-300 text-blue-700 hover:bg-blue-50"
                        >
                          <Edit className="w-4 h-4 mr-2" />
                          Edit
                        </Button>
                        <Button 
                          variant="outline" 
                          size="sm" 
                          onClick={() => handleDeletePersona(persona)}
                          className="w-full border-red-300 text-red-700 hover:bg-red-50"
                        >
                          <Trash2 className="w-4 h-4 mr-2" />
                          Delete
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            <div className="flex justify-center">
              <Button 
                onClick={handleCreateNewCharacter}
                className="flex items-center space-x-2 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white px-6 py-3 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
              >
                <UserPlus className="w-5 h-5" />
                <span className="font-semibold">Create New Persona</span>
              </Button>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default PersonaManagement; 