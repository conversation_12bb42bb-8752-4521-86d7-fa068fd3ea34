import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from './ui/button';
import { Input } from './ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Textarea, AutoResizeTextarea } from './ui/textarea';
import { ArrowLeft, Upload, Sparkles, User, Globe, Plus, UserPlus } from 'lucide-react';
import type { ElectronAPI } from '../types/electron.d';
import CharacterCreation from './CharacterCreation';

// New interfaces for the two-tier system
export interface CharacterSetting {
  id?: number;
  name: string;
  character: string; // User's role/relationship to the AI character
  personality: string;
  appearance: string; // AI character's appearance
  scenario: string;
  initial_message: string;
  example_dialogs: string;
  image_url?: string;
  created_at?: string;
}

export interface WorldSetting {
  id?: number;
  name: string;
  worldDescription: string;
  characterDescription: string;
  characterRole: string;
}

// Interface for user personas (personas the user roleplays as)
export interface UserPersona {
  id: string;
  name: string;
  personality: string;
  appearance: string;
  image?: string;
  created_at: string;
}

export interface ChatParameters {
  settingType: 'character' | 'world';
  characterSetting?: CharacterSetting;
  worldSetting?: WorldSetting;
  userPersona?: UserPersona; // Add user persona to chat parameters
  initialBootstrap?: {
    content: string;
    options: string[];
  };
  // Fields for existing chat continuation
  fullTimeline?: any; // Timeline data for existing chats
  existingChat?: any; // Original chat data for reference
  currentChatId?: number; // Current chat ID for proper branching
}

interface ChatSetupProps {
  onStartChat: (parameters: ChatParameters) => void;
  onBack: () => void;
  onNavigateToCharacterCreation?: () => void;
  editingCharacter?: {
    id?: string;
    name?: string;
    personality?: string;
    appearance?: string;
    image?: string;
    created_at?: string;
    character?: string;
    scenario?: string;
    initialMessage?: string;
    exampleDialogs?: string;
    // New structure for pre-filled data from existing chats
    settingType?: 'character' | 'world';
    characterSetting?: CharacterSetting;
    worldSetting?: WorldSetting;
    userPersona?: UserPersona;
  } | null;
}

const ChatSetup: React.FC<ChatSetupProps> = ({ onStartChat, onBack, onNavigateToCharacterCreation, editingCharacter }) => {
  const [setupType, setSetupType] = useState<'selection' | 'character' | 'world' | 'character-selection'>('selection');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [userPersonas, setUserPersonas] = useState<UserPersona[]>([]);
  const [selectedUserPersona, setSelectedUserPersona] = useState<UserPersona | null>(null);
  const [showCharacterCreation, setShowCharacterCreation] = useState(false);
  const [editingPersona, setEditingPersona] = useState<UserPersona | null>(null);

  // Character creation state
  const [characterSetting, setCharacterSetting] = useState<CharacterSetting>({
    name: '',
    character: '', // User's role/relationship to the AI character
    personality: '',
    appearance: '', // AI character's appearance
    scenario: '',
    initial_message: '',
    example_dialogs: '',
    image_url: ''
  });

  // World creation state
  const [worldSetting, setWorldSetting] = useState<WorldSetting>({
    name: '',
    worldDescription: '',
    characterDescription: '',
    characterRole: ''
  });

  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string>('');
  const [streamingField, setStreamingField] = useState<string | null>(null);
  const [streamedValue, setStreamedValue] = useState<string>('');

  // Load user personas on component mount
  useEffect(() => {
    loadUserPersonas();
  }, []);

  // Pre-fill character creation form when editingCharacter is provided
  useEffect(() => {
    if (editingCharacter) {
      console.log('🎯 ChatSetup received editingCharacter:', editingCharacter);

      // Handle new structure from Directory component (pre-filled from existing chat)
      if (editingCharacter.characterSetting) {
        console.log('📝 Pre-filling character setting:', editingCharacter.characterSetting);
        setCharacterSetting(editingCharacter.characterSetting);
        setImagePreview(editingCharacter.characterSetting.image_url || '');

        // Pre-select the user persona if provided
        if (editingCharacter.userPersona) {
          console.log('👤 Pre-selecting user persona:', editingCharacter.userPersona);
          setSelectedUserPersona(editingCharacter.userPersona);
        }

        // Go directly to character creation since persona is already selected
        setSetupType('character');
      } else if (editingCharacter.worldSetting) {
        console.log('🌍 Pre-filling world setting:', editingCharacter.worldSetting);
        setWorldSetting(editingCharacter.worldSetting);

        // Pre-select the user persona if provided
        if (editingCharacter.userPersona) {
          console.log('👤 Pre-selecting user persona:', editingCharacter.userPersona);
          setSelectedUserPersona(editingCharacter.userPersona);
        }

        // Go directly to world creation since persona is already selected
        setSetupType('world');
      } else {
        // Handle legacy structure (direct character properties)
        console.log('🔄 Using legacy structure for editingCharacter');
        setCharacterSetting({
          name: editingCharacter.name || '',
          character: editingCharacter.character || '', // User's role/relationship to the AI character
          personality: editingCharacter.personality || '',
          appearance: editingCharacter.appearance || '',
          scenario: editingCharacter.scenario || '',
          initial_message: editingCharacter.initialMessage || '',
          example_dialogs: editingCharacter.exampleDialogs || '',
          image_url: editingCharacter.image || ''
        });
        setImagePreview(editingCharacter.image || '');
        // Go to persona selection first, then character creation
        setSetupType('character-selection');
      }
    }
  }, [editingCharacter]);



  const loadUserPersonas = async () => {
    try {
      const result = await window.electronAPI.getPersonas();
      if (result.success) {
        setUserPersonas(result.personas || []);
      }
    } catch (error) {
      console.error('Error loading user personas:', error);
    }
  };

  // Handle image upload
  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedImage(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  // AI Spark functionality (now streaming) - hardened for "Initial Message" and "Example Dialogs"
  const handleAISpark = async (field: string, currentValue: string) => {
    if (!hasEnoughContext()) {
      setError('Not enough context. Please fill in some fields first.');
      return;
    }

    // Map UI field identifiers to backing store keys
    const mapFieldKey = (f: string) => {
      if (setupType === 'character') {
        if (f === 'initialMessage') return 'initial_message';
        if (f === 'exampleDialogs') return 'example_dialogs';
      }
      return f;
    };

    const backingKey = mapFieldKey(field);

    // Preserve original content for the active field to avoid "vanish" perception
    const original =
      setupType === 'character'
        ? (characterSetting as any)[backingKey]
        : (worldSetting as any)[backingKey];

    setIsLoading(true);
    setError('');
    setStreamingField(field);
    setStreamedValue('');

    try {
      const context = buildContextForAI();
      const prompt = generatePromptForField(field, context);

      const apiKey = await getOpenRouterApiKey();
      const response = await fetch("https://openrouter.ai/api/v1/chat/completions", {
        method: "POST",
        headers: {
          "Authorization": `Bearer ${apiKey}`,
          "HTTP-Referer": window.location.origin,
          "X-Title": "Electrobox Chat Setup",
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          "model": "deepseek/deepseek-chat-v3-0324",
          "messages": [
            {
              "role": "user",
              "content": prompt
            }
          ],
          "stream": true
        })
      });

      if (!response.ok || !response.body) {
        throw new Error(`Failed to get AI response: ${response.status}`);
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder('utf-8');
      let done = false;
      let fullText = '';
      let buffer = '';

      while (!done) {
        const { value, done: doneReading } = await reader.read();
        done = doneReading;
        if (value) {
          buffer += decoder.decode(value, { stream: !done });
          // Split on newlines for SSE format
          const lines = buffer.split('\n');
          buffer = lines.pop() || '';
          for (const line of lines) {
            if (!line.startsWith('data:')) continue;
            const dataStr = line.slice(5).trim();
            if (!dataStr || dataStr === '[DONE]') continue;
            try {
              const data = JSON.parse(dataStr);
              const delta = data.choices?.[0]?.delta?.content || '';
              if (delta) {
                fullText += delta;
                // Do not overwrite the form state while streaming; show in separate buffer
                setStreamedValue(fullText);
              }
            } catch {
              // ignore malformed chunk
            }
          }
        }
      }

      // Final: update proper backing state key
      if (setupType === 'character') {
        setCharacterSetting(prev => ({
          ...prev,
          [backingKey]: fullText
        }));
      } else {
        setWorldSetting(prev => ({
          ...prev,
          [backingKey]: fullText
        }));
      }

    } catch (err) {
      // On any error, restore the original field value so text never vanishes
      if (setupType === 'character') {
        setCharacterSetting(prev => ({
          ...prev,
          [backingKey]: original
        }));
      } else {
        setWorldSetting(prev => ({
          ...prev,
          [backingKey]: original
        }));
      }
      setError('Failed to generate AI content. Please try again.');
    } finally {
      // Clear streaming UI state after updating/restoring the backing store
      setStreamingField(null);
      setStreamedValue('');
      setIsLoading(false);
    }
  };

  // Helper functions
  const hasEnoughContext = () => {
    if (setupType === 'character') {
      return characterSetting.name || characterSetting.character || characterSetting.personality || characterSetting.appearance;
    } else {
      return worldSetting.name || worldSetting.worldDescription || worldSetting.characterDescription;
    }
  };

  const buildContextForAI = () => {
    if (setupType === 'character') {
      return {
        aiCharacterName: characterSetting.name,
        userRoleInRelationToAICharacter: characterSetting.character,
        aiCharacterPersonality: characterSetting.personality,
        aiCharacterAppearance: characterSetting.appearance,
        conversationScenario: characterSetting.scenario,
        userPersona: selectedUserPersona ? {
          name: selectedUserPersona.name,
          personality: selectedUserPersona.personality,
          appearance: selectedUserPersona.appearance
        } : null
      };
    } else {
      return {
        worldName: worldSetting.name,
        worldDescription: worldSetting.worldDescription,
        userCharacterDescription: worldSetting.characterDescription,
        userCharacterRole: worldSetting.characterRole,
        userPersona: selectedUserPersona ? {
          name: selectedUserPersona.name,
          personality: selectedUserPersona.personality,
          appearance: selectedUserPersona.appearance
        } : null
      };
    }
  };

  const generatePromptForField = (field: string, context: any) => {
    const contextStr = Object.entries(context)
      .filter(([_, value]) => value && _ !== 'userPersona')
      .map(([key, value]) => `${key}: ${value}`)
      .join('\n');

    const userPersonaStr = context.userPersona ? 
      `\n\nUSER PERSONA (who the user is roleplaying as):\nName: ${context.userPersona.name}\nPersonality: ${context.userPersona.personality}\nAppearance: ${context.userPersona.appearance}` : '';

    const prompts = {
      character: `Based on this context:\n${contextStr}${userPersonaStr}\n\nIMPORTANT: You are creating an AI character that the user will interact with. The user has a persona they roleplay as (shown above).\n\nDefine the user's role, relationship, or identity in relation to the AI character being created. Who is the user to this AI character? What is their relationship or position? Consider the user's persona when defining this relationship.`,
      personality: `Based on this context:\n${contextStr}${userPersonaStr}\n\nIMPORTANT: You are creating an AI character that the user will interact with. The user has a persona they roleplay as (shown above).\n\nDescribe the AI character's personality, mannerisms, speech patterns, and behavioral traits. This is for the AI character being created, NOT the user's persona. Make it engaging and specific.`,
      appearance: `Based on this context:\n${contextStr}${userPersonaStr}\n\nIMPORTANT: You are creating an AI character that the user will interact with. The user has a persona they roleplay as (shown above).\n\nDescribe the AI character's physical appearance, clothing, style, and visual characteristics. This is for the AI character being created, NOT the user's persona. Be detailed and vivid in describing their looks, attire, and any distinctive visual features.`,
      scenario: `Based on this context:\n${contextStr}${userPersonaStr}\n\nIMPORTANT: You are creating an AI character that the user will interact with. The user has a persona they roleplay as (shown above).\n\nDescribe the world, setting, location, time period, and broader circumstances where the interaction between the user and AI character takes place. Focus on the environmental and contextual backdrop rather than the immediate conversation situation.`,
      initialMessage: `Based on this context:\n${contextStr}${userPersonaStr}\n\nIMPORTANT: You are creating an AI character that the user will interact with. The user has a persona they roleplay as (shown above).\n\nWrite a lengthy, engaging first message from the AI character being created. This message should be directed to the user (who is roleplaying as their persona). Make it immersive and encourage detailed responses.`,
      exampleDialogs: `Based on this context:\n${contextStr}${userPersonaStr}\n\nIMPORTANT: You are creating an AI character that the user will interact with. The user has a persona they roleplay as (shown above).\n\nCreate example dialog exchanges using {{char}} (for the AI character being created) and {{user}} (for the user with their persona) format. Show the AI character's personality through conversation.`,
      worldDescription: `Based on this context:\n${contextStr}${userPersonaStr}\n\nIMPORTANT: You are creating a world setting where the user (with their persona) will interact with AI characters.\n\nCreate a rich, detailed world description including setting, atmosphere, key locations, and important background information.`,
      characterDescription: `Based on this context:\n${contextStr}${userPersonaStr}\n\nIMPORTANT: You are creating a world setting where the user (with their persona) will interact with AI characters.\n\nDescribe the user's character in this world - their appearance, background, and current situation. This refers to the user's persona in this world setting.`,
      characterRole: `Based on this context:\n${contextStr}${userPersonaStr}\n\nIMPORTANT: You are creating a world setting where the user (with their persona) will interact with AI characters.\n\nDefine the user character's role, position, or significance within this world setting. This refers to the user's persona's role in this world.`
    };

    return prompts[field as keyof typeof prompts] || `Generate content for ${field} based on: ${contextStr}${userPersonaStr}`;
  };

  const updateFieldWithAIResponse = (field: string, response: string) => {
    if (setupType === 'character') {
      setCharacterSetting(prev => ({
        ...prev,
        [field]: response
      }));
    } else {
      setWorldSetting(prev => ({
        ...prev,
        [field]: response
      }));
    }
  };

  // Helper function to parse generated content and extract options
  const parseGeneratedContent = async (content: string): Promise<string[] | null> => {
    console.log('Raw generated content:', content);
    
    // First, try to parse as JSON directly
    try {
      const responseData = JSON.parse(content);
      console.log('Parsed response data:', responseData);
      
      if (responseData.options && Array.isArray(responseData.options)) {
        const options = responseData.options.slice(0, 4);
        console.log('Extracted options from JSON:', options);
        
        if (options.length === 4) {
          console.log('Successfully generated 4 AI options:', options);
          return options;
        } else {
          console.warn('Not enough options generated:', options.length);
        }
      } else {
        console.warn('No options array found in response:', responseData);
      }
    } catch (e) {
      console.error('Failed to parse JSON options:', e);
      
      // Try to extract options from malformed JSON using regex
      const optionsMatch = content.match(/"options"\s*:\s*\[(.*?)\]/s);
      if (optionsMatch) {
        try {
          const optionsText = optionsMatch[1];
          console.log('Extracted options text:', optionsText);
          
          // More robust parsing of the options array
          const options = optionsText
            .split(',')
            .map(opt => {
              // Remove quotes and extra whitespace
              const cleaned = opt.trim().replace(/^["']|["']$/g, '');
              // Remove any trailing commas or brackets
              return cleaned.replace(/[,\]]+$/, '');
            })
            .filter(opt => opt.length > 0 && opt !== ']' && opt !== ',')
            .slice(0, 4);
          
          console.log('Extracted options from malformed JSON:', options);
          
          if (options.length === 4) {
            console.log('Successfully extracted 4 options from malformed JSON:', options);
            return options;
          } else {
            console.warn('Not enough options extracted from malformed JSON:', options.length);
          }
        } catch (extractError) {
          console.error('Failed to extract options from malformed JSON:', extractError);
        }
      }
      
      // Try to extract options from plain text format
      const lines = content.split('\n').filter(line => line.trim().length > 0);
      const textOptions = lines
        .map(line => line.trim())
        .filter(line => {
          // Filter out lines that are clearly not options
          const lowerLine = line.toLowerCase();
          return !lowerLine.includes('json') && 
                 !lowerLine.includes('{') && 
                 !lowerLine.includes('}') && 
                 !lowerLine.includes('options') &&
                 !lowerLine.includes('[') &&
                 !lowerLine.includes(']') &&
                 line.length > 5; // Minimum reasonable option length
        })
        .slice(0, 4);
      
      if (textOptions.length === 4) {
        console.log('Extracted options from plain text:', textOptions);
        return textOptions;
      } else if (textOptions.length > 0) {
        console.log('Extracted some options from plain text, but not 4:', textOptions);
      }
    }
    
    return null;
  };

  // Generate AI options for chat interactions
  const generateAIOptions = async (settingType: 'character' | 'world', setting: any): Promise<string[]> => {
    try {
      console.log('Generating AI options for:', settingType, setting);
      
      const apiKey = await getOpenRouterApiKey();
      if (!apiKey) {
        console.log('No API key found, using default options');
        // Return default options if no API key
        return [
          'Continue the conversation naturally and engage with the character',
          'Ask a thoughtful question about their background or current situation',
          'Take a specific action based on your character\'s personality and goals',
          'Observe the situation carefully and react to the environment around you'
        ];
      }

      console.log('API key found, proceeding with AI generation');

      let prompt = '';
      if (settingType === 'character') {
        prompt = `Based on this AI character setup:
AI Character Name: ${setting.name}
User's Role in Relation to AI Character: ${setting.character}
AI Character's Personality: ${setting.personality}
AI Character's Appearance: ${setting.appearance}
Conversation Scenario: ${setting.scenario}
AI Character's Initial Message: ${setting.initialMessage}

IMPORTANT: You are generating response options for the USER to choose from when interacting with this AI character. The user has their own persona they roleplay as.

Generate exactly 4 different response options that the user might naturally choose. These can be any type of action, response, or interaction that feels authentic to the situation. Don't limit yourself to specific categories - just think of what the users persona might do in this context.

CRITICAL: You MUST respond with ONLY a valid JSON object in this exact format, with no additional text or explanation:
{
  "options": [
    "option 1 text",
    "option 2 text",
    "option 3 text",
    "option 4 text"
  ]
}`;
      } else {
        prompt = `Based on this world setup:
World Name: ${setting.name}
World Description: ${setting.worldDescription}
User's Character Description: ${setting.characterDescription}
User's Character Role: ${setting.characterRole}

IMPORTANT: You are generating response options for the USER to choose from when interacting in this world. The user has their own persona they roleplay as.

Generate exactly 4 different response options that the user might naturally choose. These can be any type of action, response, or interaction that feels authentic to the situation. Don't limit yourself to specific categories - just think of what the users persona might do in this context.

CRITICAL: You MUST respond with ONLY a valid JSON object in this exact format, with no additional text or explanation:
{
  "options": [
    "option 1 text",
    "option 2 text",
    "option 3 text", 
    "option 4 text"
  ]
}`;
      }

      console.log('Sending prompt to AI:', prompt);

      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': window.location.origin,
          'X-Title': 'Electrobox Chat'
        },
        body: JSON.stringify({
          model: 'deepseek/deepseek-chat-v3-0324',
          messages: [
            {
              role: 'user',
              content: prompt
            }
          ],
          max_tokens: 10000,
          temperature: 0.3,
          response_format: { type: 'json_object' },
          top_p: 0.9,
          frequency_penalty: 0.1,
          presence_penalty: 0.1
        })
      });

      console.log('AI response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('API request failed:', response.status, errorText);
        throw new Error(`API request failed: ${response.status} - ${errorText}`);
      }

      const data = await response.json();
      console.log('AI response data:', data);
      
      const generatedContent = data.choices?.[0]?.message?.content?.trim();
      console.log('Generated content:', generatedContent);
      
      // If we got content, try to parse it
      if (generatedContent) {
        const parsedOptions = await parseGeneratedContent(generatedContent);
        if (parsedOptions && parsedOptions.length === 4) {
          return parsedOptions;
        }
      }
      
      // If first attempt failed, try with a different model
      console.log('First attempt failed, trying with alternative model...');
      const alternativeResponse = await fetch('https://openrouter.ai/api/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': window.location.origin,
          'X-Title': 'Electrobox Chat'
        },
        body: JSON.stringify({
          model: 'anthropic/claude-3.5-sonnet',
          messages: [
            {
              role: 'user',
              content: prompt
            }
          ],
          max_tokens: 10000,
          temperature: 0.3,
          response_format: { type: 'json_object' }
        })
      });

      if (alternativeResponse.ok) {
        const alternativeData = await alternativeResponse.json();
        const alternativeContent = alternativeData.choices?.[0]?.message?.content?.trim();
        
        if (alternativeContent) {
          const alternativeOptions = await parseGeneratedContent(alternativeContent);
          if (alternativeOptions && alternativeOptions.length === 4) {
            return alternativeOptions;
          }
        }
      }

      // If we still don't have options, fall back to defaults
      console.warn('All parsing attempts failed, using fallback options');
    } catch (err) {
      console.error('Failed to generate AI options:', err);
    }

    console.log('Falling back to default options');
    // Fallback options
    return settingType === 'character'
      ? [
          'Act according to personality',
          'Engage with the scenario',
          'Explore surroundings',
          'Reflect on situation'
        ]
      : [
          'Embrace your role',
          'Explore the world',
          'Seek interaction',
          'Adapt to circumstances'
        ];
  };

  const getOpenRouterApiKey = async () => {
    try {
      console.log('Attempting to get API key...');
      const result = await window.electronAPI.getActiveApiKey();
      console.log('API key result:', result);
      
      if (result.success && result.apiKey) {
        console.log('API key found successfully');
        return result.apiKey.api_key;
      }
      
      console.warn('No API key found in result:', result);
      throw new Error('No API key found');
    } catch (err) {
      console.error('Error getting API key:', err);
      throw new Error('Failed to get API key');
    }
  };

  // Handle persona selection
  const handlePersonaSelection = (persona: UserPersona) => {
    setSelectedUserPersona(persona);
    // If we're editing an existing character, go to character creation
    // Otherwise go back to the selection screen
    if (setupType === 'character-selection' && editingCharacter) {
      setSetupType('character');
    } else if (setupType === 'character-selection') {
      setSetupType('selection');
    }
  };

  // Handle creating new character
  const handleCreateNewCharacter = () => {
    setEditingPersona(null); // Clear any editing state
    if (onNavigateToCharacterCreation) {
      onNavigateToCharacterCreation();
    } else {
      setShowCharacterCreation(true);
    }
  };

  // Handle character creation/editing completion
  const handleCharacterCreationComplete = () => {
    setShowCharacterCreation(false);
    setEditingPersona(null);
    loadUserPersonas(); // Reload personas to get updated data
  };

  // Handle editing persona
  const handleEditPersona = (persona: UserPersona) => {
    setEditingPersona(persona);
    setShowCharacterCreation(true);
  };

  // Form submission handlers
  const handleCharacterSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate required fields
    if (!characterSetting.name || !characterSetting.character || !characterSetting.personality ||
        !characterSetting.appearance || !characterSetting.scenario || !characterSetting.initial_message || !characterSetting.example_dialogs) {
      setError('Please fill in all required fields');
      return;
    }

    // Ensure user has selected a persona to roleplay as
    if (!selectedUserPersona) {
      setError('Please select a persona to roleplay as');
      return;
    }

    setIsLoading(true);
    try {
      // If we're editing an existing character (legacy mode with id), just start the chat
      if (editingCharacter && editingCharacter.id) {
        // Generate AI options for the existing character
        const aiOptions = await generateAIOptions('character', characterSetting);

        // Create initial chat entry with AI-generated options
        const initialContent = characterSetting.initial_message;
        const chatContent = {
          content: initialContent,
          options: aiOptions,
          userPersona: selectedUserPersona
        };

        const initialChatResult = await window.electronAPI.saveChat(
          null, // no parent
          parseInt(editingCharacter.id),
          'character',
          JSON.stringify(chatContent),
          selectedUserPersona?.id || null, // Pass user persona ID
          parseInt(editingCharacter.id) // Pass character ID
        );

        // Populate bootstrap so RPGChat can render the correct initial 4 options immediately
        try {
          window.electronAPI.setInitialChatBootstrap('character', parseInt(editingCharacter.id), {
            content: chatContent.content,
            options: chatContent.options
          });
        } catch (e) {
          console.warn('Failed to set initial chat bootstrap (character):', e);
        }

        if (initialChatResult.success) {
          const parameters: ChatParameters = {
            settingType: 'character',
            characterSetting: {
              ...characterSetting,
              id: parseInt(editingCharacter.id),
              image_url: imagePreview
            },
            userPersona: selectedUserPersona,
            initialBootstrap: {
              content: chatContent.content,
              options: chatContent.options
            }
          };

          onStartChat(parameters);
        } else {
          setError('Failed to create initial chat message');
        }
        return;
      }

      // Otherwise, save as new character setting to database
      const settingToSave = {
        ...characterSetting,
        image_url: imagePreview
      };

      const result = await window.electronAPI.saveCharacterSetting(settingToSave);

      if (result.success) {
        // Generate AI options for the initial message
        const aiOptions = await generateAIOptions('character', settingToSave);

        // Create initial chat entry with AI-generated options
        // Ensure we read the latest state value for initial_message,
        // not a possibly stale closure value during streaming.
        const initialContent = (prev => prev.initial_message)(characterSetting);

        const chatContent = {
          content: initialContent,
          options: aiOptions,
          userPersona: selectedUserPersona // Include user persona in chat
        };

        const initialChatResult = await window.electronAPI.saveChat(
          null, // no parent
          result.id,
          'character',
          JSON.stringify(chatContent),
          selectedUserPersona?.id || null, // Pass user persona ID
          result.id // Pass character ID (same as setting ID for new characters)
        );

        // Populate bootstrap so RPGChat can render the correct initial 4 options immediately
        try {
          window.electronAPI.setInitialChatBootstrap('character', result.id, {
            content: chatContent.content,
            options: chatContent.options
          });
        } catch (e) {
          console.warn('Failed to set initial chat bootstrap (character):', e);
        }

        if (initialChatResult.success) {
          const parameters: ChatParameters = {
            settingType: 'character',
            characterSetting: {
              ...settingToSave,
              id: result.id
            },
            userPersona: selectedUserPersona,
            initialBootstrap: {
              content: chatContent.content,
              options: chatContent.options
            }
          };

          onStartChat(parameters);
        } else {
          setError('Failed to create initial chat message');
        }
      } else {
        setError(result.error || 'Failed to save character setting');
      }
    } catch (err) {
      setError('Failed to create character. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleWorldSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate required fields
    if (!worldSetting.name || !worldSetting.worldDescription ||
        !worldSetting.characterDescription || !worldSetting.characterRole) {
      setError('Please fill in all required fields');
      return;
    }

    // Ensure user has selected a persona to roleplay as
    if (!selectedUserPersona) {
      setError('Please select a persona to roleplay as');
      return;
    }

    setIsLoading(true);
    try {
      // Save world setting to database
      const result = await window.electronAPI.saveWorldSetting(worldSetting);

      if (result.success) {
        // Generate AI options for the world setting
        const aiOptions = await generateAIOptions('world', worldSetting);

        // Create initial chat entry with a generated opening message and AI options
        const initialMessage = `Welcome to ${worldSetting.name}. ${worldSetting.worldDescription}`;
        const chatContent = {
          content: initialMessage,
          options: aiOptions,
          userPersona: selectedUserPersona // Include user persona in chat
        };

        const initialChatResult = await window.electronAPI.saveChat(
          null, // no parent
          result.id,
          'world',
          JSON.stringify(chatContent),
          selectedUserPersona?.id || null, // Pass user persona ID
          null // No character ID for world settings
        );

        // Populate bootstrap so RPGChat can render the correct initial 4 options immediately
        try {
          window.electronAPI.setInitialChatBootstrap('world', result.id, {
            content: chatContent.content,
            options: chatContent.options
          });
        } catch (e) {
          console.warn('Failed to set initial chat bootstrap (world):', e);
        }

        if (initialChatResult.success) {
          const parameters: ChatParameters = {
            settingType: 'world',
            worldSetting: {
              ...worldSetting,
              id: result.id
            },
            userPersona: selectedUserPersona,
            initialBootstrap: {
              content: chatContent.content,
              options: chatContent.options
            }
          };

          onStartChat(parameters);
        } else {
          setError('Failed to create initial chat message');
        }
      } else {
        setError(result.error || 'Failed to save world setting');
      }
    } catch (err) {
      setError('Failed to create world. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Render character selection
  const renderCharacterSelection = () => (
    <div className="max-w-6xl mx-auto">
      <div className="flex items-center mb-6">
        <Button 
          variant="ghost" 
          size="sm" 
          onClick={() => {
            // If we have a selected persona, go back to the creation screen
            if (selectedUserPersona) {
              setSetupType('selection');
            } else {
              onBack();
            }
          }} 
          className="mr-4"
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <h1 className="text-3xl font-bold text-foreground">Choose Your Persona</h1>
      </div>

      <div className="mb-6">
        <p className="text-muted-foreground mb-4">
          Select a persona to roleplay as, or create a new one.
        </p>
        
        {selectedUserPersona && (
          <Card className="mb-6 border-green-200 bg-green-50">
            <CardHeader>
              <CardTitle className="text-green-800 flex items-center justify-between">
                <span>Selected Persona</span>
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={() => setSelectedUserPersona(null)}
                  className="border-green-300 text-green-700 hover:bg-green-100"
                >
                  Change Selection
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-4">
                {selectedUserPersona.image ? (
                  <img 
                    src={selectedUserPersona.image} 
                    alt={selectedUserPersona.name}
                    className="w-16 h-16 rounded-full object-cover border-2 border-green-200"
                  />
                ) : (
                  <div className="w-16 h-16 rounded-full bg-green-200 flex items-center justify-center border-2 border-green-300">
                    <User className="w-8 h-8 text-green-600" />
                  </div>
                )}
                <div className="flex-1">
                  <h3 className="font-semibold text-lg text-green-800">{selectedUserPersona.name}</h3>
                  <p className="text-sm text-green-700 mb-2">{selectedUserPersona.personality}</p>
                  {selectedUserPersona.appearance && (
                    <p className="text-xs text-green-600">{selectedUserPersona.appearance}</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-6">
        {userPersonas.map((persona) => (
          <Card 
            key={persona.id} 
            className={`cursor-pointer hover:shadow-lg transition-all duration-200 transform hover:scale-105 ${
              selectedUserPersona?.id === persona.id 
                ? 'ring-2 ring-blue-500 bg-blue-50 shadow-lg scale-105' 
                : 'hover:ring-2 hover:ring-blue-200'
            }`}
            onClick={() => handlePersonaSelection(persona)}
          >
            <CardHeader className="pb-3">
              <div className="flex flex-col items-center text-center space-y-3">
                {persona.image ? (
                  <img 
                    src={persona.image} 
                    alt={persona.name}
                    className="w-20 h-20 rounded-full object-cover border-2 border-gray-200 shadow-md"
                  />
                ) : (
                  <div className="w-20 h-20 rounded-full bg-gradient-to-br from-blue-100 to-blue-200 flex items-center justify-center border-2 border-blue-300 shadow-md">
                    <User className="w-10 h-10 text-blue-600" />
                  </div>
                )}
                <div>
                  <CardTitle className="text-lg font-bold">{persona.name}</CardTitle>
                  <CardDescription className="text-xs">
                    Created {new Date(persona.created_at).toLocaleDateString()}
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="space-y-2">
                <div>
                  <h4 className="text-sm font-semibold text-gray-700 mb-1">Personality</h4>
                  <p className="text-sm text-muted-foreground line-clamp-3 leading-relaxed">
                    {persona.personality}
                  </p>
                </div>
                {persona.appearance && (
                  <div>
                    <h4 className="text-sm font-semibold text-gray-700 mb-1">Appearance</h4>
                    <p className="text-sm text-muted-foreground line-clamp-2 leading-relaxed">
                      {persona.appearance}
                    </p>
                  </div>
                )}
                <div className="pt-2">
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={(e) => {
                      e.stopPropagation();
                      handleEditPersona(persona);
                    }}
                    className="w-full border-gray-300 text-gray-700 hover:bg-gray-50"
                  >
                    Edit Persona
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="flex justify-center">
        <Button 
          onClick={handleCreateNewCharacter}
          className="flex items-center space-x-2 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white px-6 py-3 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
        >
          <UserPlus className="w-5 h-5" />
          <span className="font-semibold">Create New Persona</span>
        </Button>
      </div>
    </div>
  );

  // Render setup type selection
  const renderSetupSelection = () => (
    <div className="max-w-2xl mx-auto">
      <div className="flex items-center mb-6">
        <Button variant="ghost" size="sm" onClick={onBack} className="mr-4">
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <h1 className="text-3xl font-bold text-foreground">Create New Chat</h1>
      </div>

      {selectedUserPersona && (
        <Card className="mb-6 border-blue-200 bg-blue-50">
          <CardContent className="p-4">
            <div className="flex flex-col items-center space-y-3">
              <div className="text-center">
                <p className="text-blue-800 font-medium">
                  Roleplaying as: <span className="font-bold">{selectedUserPersona.name}</span>
                </p>
              </div>
              {selectedUserPersona.image ? (
                <img 
                  src={selectedUserPersona.image} 
                  alt={selectedUserPersona.name}
                  className="w-32 h-32 object-contain border-2 border-blue-200 shadow-md rounded-lg"
                />
              ) : (
                <div className="w-32 h-32 bg-gradient-to-br from-blue-100 to-blue-200 flex items-center justify-center border-2 border-blue-300 shadow-md rounded-lg">
                  <User className="w-16 h-16 text-blue-600" />
                </div>
              )}
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => setSetupType('character-selection')}
                className="border-blue-300 text-blue-700 hover:bg-blue-100"
              >
                Change Persona
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {!selectedUserPersona && (
        <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <p className="text-yellow-800">
            Please select a persona to roleplay as first.
          </p>
          <Button 
            onClick={() => setSetupType('character-selection')}
            className="mt-2"
          >
            Choose Persona
          </Button>
        </div>
      )}

      <div className="space-y-6">
        <Card 
          className={`cursor-pointer hover:shadow-lg transition-shadow ${
            !selectedUserPersona ? 'opacity-50 cursor-not-allowed' : ''
          }`}
          onClick={() => selectedUserPersona && setSetupType('character')}
        >
          <CardHeader>
            <CardTitle className="flex items-center gap-3">
              <User className="h-6 w-6" />
              Create a Character
            </CardTitle>
            <CardDescription>
              Design a character with personality, backstory, and conversation style for character-based roleplay
            </CardDescription>
          </CardHeader>
        </Card>

        <Card 
          className={`cursor-pointer hover:shadow-lg transition-shadow ${
            !selectedUserPersona ? 'opacity-50 cursor-not-allowed' : ''
          }`}
          onClick={() => selectedUserPersona && setSetupType('world')}
        >
          <CardHeader>
            <CardTitle className="flex items-center gap-3">
              <Globe className="h-6 w-6" />
              Create a World
            </CardTitle>
            <CardDescription>
              Build a world or scenario with detailed setting and define your character's role within it
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    </div>
  );

  // AI Spark button component
  const AISparkButton = ({ field, disabled }: { field: string; disabled?: boolean }) => (
    <Button
      type="button"
      variant="outline"
      size="sm"
      onClick={() => handleAISpark(field, '')}
      disabled={disabled || isLoading || streamingField === field}
      className="ml-2"
    >
      <Sparkles className="h-4 w-4" />
      {streamingField === field && (
        <span className="ml-1 animate-pulse">...</span>
      )}
    </Button>
  );

  // Render character creation form
  const renderCharacterCreation = () => (
    <div className="max-w-2xl mx-auto">
      <div className="flex items-center mb-6">
        <Button variant="ghost" size="sm" onClick={() => setSetupType('selection')} className="mr-4">
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <h1 className="text-3xl font-bold text-foreground">Create a Character</h1>
      </div>

      {error && (
        <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          {error}
        </div>
      )}

      {/* Current Persona Display */}
      {selectedUserPersona && (
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Your Current Persona</span>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => setSetupType('character-selection')}
              >
                Change Persona
              </Button>
            </CardTitle>
          </CardHeader>
          <CardHeader className="pb-3">
            <div className="flex flex-col items-center text-center space-y-3">
              {selectedUserPersona.image ? (
                <img 
                  src={selectedUserPersona.image} 
                  alt={selectedUserPersona.name}
                  className="w-20 h-20 rounded-full object-cover border-2 border-gray-200 shadow-md"
                />
              ) : (
                <div className="w-20 h-20 rounded-full bg-gradient-to-br from-blue-100 to-blue-200 flex items-center justify-center border-2 border-blue-300 shadow-md">
                  <User className="w-10 h-10 text-blue-600" />
                </div>
              )}
              <div>
                <CardTitle className="text-lg font-bold">{selectedUserPersona.name}</CardTitle>
                <CardDescription className="text-xs">
                  Created {new Date(selectedUserPersona.created_at).toLocaleDateString()}
                </CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="space-y-2">
              <div>
                <h4 className="text-sm font-semibold text-gray-700 mb-1">Personality</h4>
                <p className="text-sm text-muted-foreground line-clamp-3 leading-relaxed">
                  {selectedUserPersona.personality}
                </p>
              </div>
              {selectedUserPersona.appearance && (
                <div>
                  <h4 className="text-sm font-semibold text-gray-700 mb-1">Appearance</h4>
                  <p className="text-sm text-muted-foreground line-clamp-2 leading-relaxed">
                    {selectedUserPersona.appearance}
                  </p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      <form onSubmit={handleCharacterSubmit} className="space-y-6">
        {/* Image Upload */}
        <Card>
          <CardHeader>
            <CardTitle>Character Avatar</CardTitle>
            <CardDescription>Upload an image for your character</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center gap-4">
                <Input
                  type="file"
                  accept="image/*"
                  onChange={handleImageUpload}
                  className="flex-1"
                />
                <Upload className="h-5 w-5 text-muted-foreground" />
              </div>
              {imagePreview && (
                <div className="mt-4">
                  <img
                    src={imagePreview}
                    alt="Character preview"
                    className="w-32 h-32 object-cover rounded-lg border"
                  />
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Character Name */}
        <Card>
          <CardHeader>
            <CardTitle>Character Name</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <Input
                value={streamingField === 'name' ? streamedValue : characterSetting.name}
                onChange={(e) => setCharacterSetting(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Enter character's name"
                required
                className="flex-1"
                disabled={streamingField === 'name'}
              />
              <AISparkButton field="name" />
            </div>
          </CardContent>
        </Card>

        {/* User's Role/Persona */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              Your Role/Persona
              <AISparkButton field="character" />
            </CardTitle>
            <CardDescription>Who you are to this character - your relationship, role, or identity in their world</CardDescription>
          </CardHeader>
          <CardContent>
            <AutoResizeTextarea
              value={streamingField === 'character' ? streamedValue : characterSetting.character}
              onChange={(e) => setCharacterSetting(prev => ({ ...prev, character: e.target.value }))}
              placeholder="Describe who you are to this character - your relationship, role, identity, or how they should see you..."
              required
              disabled={streamingField === 'character'}
            />
          </CardContent>
        </Card>

        {/* Personality */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              Personality
              <AISparkButton field="personality" />
            </CardTitle>
            <CardDescription>Describe the character's persona here</CardDescription>
          </CardHeader>
          <CardContent>
            <AutoResizeTextarea
              value={streamingField === 'personality' ? streamedValue : characterSetting.personality}
              onChange={(e) => setCharacterSetting(prev => ({ ...prev, personality: e.target.value }))}
              placeholder="Describe personality traits, mannerisms, speech patterns..."
              required
              disabled={streamingField === 'personality'}
            />
          </CardContent>
        </Card>

        {/* Appearance */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              Appearance
              <AISparkButton field="appearance" />
            </CardTitle>
            <CardDescription>Describe the AI character's physical appearance, clothing, and visual characteristics</CardDescription>
          </CardHeader>
          <CardContent>
            <AutoResizeTextarea
              value={streamingField === 'appearance' ? streamedValue : characterSetting.appearance}
              onChange={(e) => setCharacterSetting(prev => ({ ...prev, appearance: e.target.value }))}
              placeholder="Describe the character's physical appearance, clothing, style, and visual traits..."
              required
              disabled={streamingField === 'appearance'}
            />
          </CardContent>
        </Card>

        {/* Scenario */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              Scenario
              <AISparkButton field="scenario" />
            </CardTitle>
            <CardDescription>The world, setting, and broader circumstances where the interaction takes place</CardDescription>
          </CardHeader>
          <CardContent>
            <AutoResizeTextarea
              value={streamingField === 'scenario' ? streamedValue : characterSetting.scenario}
              onChange={(e) => setCharacterSetting(prev => ({ ...prev, scenario: e.target.value }))}
              placeholder="Describe the world, setting, location, time period, and broader context..."
              required
              disabled={streamingField === 'scenario'}
            />
          </CardContent>
        </Card>

        {/* Initial Message */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              Initial Message
              <AISparkButton field="initialMessage" />
            </CardTitle>
            <CardDescription>First message from your character. Provide a lengthy first message to encourage the character to give longer responses</CardDescription>
          </CardHeader>
          <CardContent>
            <AutoResizeTextarea
              value={streamingField === 'initialMessage' ? streamedValue : characterSetting.initial_message}
              onChange={(e) => {
                // If streaming, ignore user edits to prevent conflicts and perceived "vanish"
                if (streamingField === 'initialMessage') return;
                setCharacterSetting(prev => ({ ...prev, initial_message: e.target.value }));
              }}
              placeholder="Write the character's opening message..."
              required
              disabled={streamingField === 'initialMessage'}
            />
          </CardContent>
        </Card>

        {/* Example Dialogs */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              Example Dialogs
              <AISparkButton field="exampleDialogs" />
            </CardTitle>
          </CardHeader>
          <CardContent>
            <AutoResizeTextarea
              value={streamingField === 'exampleDialogs' ? streamedValue : characterSetting.example_dialogs}
              onChange={(e) => {
                // Prevent edits while streaming to avoid conflicts and perceived vanish
                if (streamingField === 'exampleDialogs') return;
                setCharacterSetting(prev => ({ ...prev, example_dialogs: e.target.value }));
              }}
              placeholder={`{{char}}: Hey, im Mark\n{{user}}: hello Mark\n{{char}}: nice to meet you :)`}
              required
              disabled={streamingField === 'exampleDialogs'}
            />
          </CardContent>
        </Card>

        {/* Submit Button */}
        <div className="flex justify-end space-x-4">
          <Button type="button" variant="outline" onClick={() => setSetupType('selection')}>
            Back
          </Button>
          <Button type="submit" disabled={isLoading}>
            {isLoading ? 'Creating...' : 'Start Chat'}
          </Button>
        </div>
      </form>
    </div>
  );

  // Render world creation form
  const renderWorldCreation = () => (
    <div className="max-w-2xl mx-auto">
      <div className="flex items-center mb-6">
        <Button variant="ghost" size="sm" onClick={() => setSetupType('selection')} className="mr-4">
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <h1 className="text-3xl font-bold text-foreground">Create a World</h1>
      </div>

      {error && (
        <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          {error}
        </div>
      )}

      {/* Current Persona Display */}
      {selectedUserPersona && (
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Your Current Persona</span>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => setSetupType('character-selection')}
              >
                Change Persona
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-4">
              {selectedUserPersona.image ? (
                <img 
                  src={selectedUserPersona.image} 
                  alt={selectedUserPersona.name}
                  className="w-16 h-16 rounded-full object-cover border-2 border-gray-200"
                />
              ) : (
                <div className="w-16 h-16 rounded-full bg-gray-200 flex items-center justify-center border-2 border-gray-300">
                  <User className="w-8 h-8 text-gray-500" />
                </div>
              )}
              <div className="flex-1">
                <h3 className="font-semibold text-lg">{selectedUserPersona.name}</h3>
                <p className="text-sm text-muted-foreground mb-2">{selectedUserPersona.personality}</p>
                {selectedUserPersona.appearance && (
                  <p className="text-xs text-muted-foreground">{selectedUserPersona.appearance}</p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <form onSubmit={handleWorldSubmit} className="space-y-6">
        {/* World Name */}
        <Card>
          <CardHeader>
            <CardTitle>World Name</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <Input
                value={streamingField === 'name' ? streamedValue : worldSetting.name}
                onChange={(e) => setWorldSetting(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Enter world/scenario name"
                required
                className="flex-1"
                disabled={streamingField === 'name'}
              />
              <AISparkButton field="name" />
            </div>
          </CardContent>
        </Card>

        {/* World Description */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              World Description
              <AISparkButton field="worldDescription" />
            </CardTitle>
          </CardHeader>
          <CardContent>
            <AutoResizeTextarea
              value={streamingField === 'worldDescription' ? streamedValue : worldSetting.worldDescription}
              onChange={(e) => setWorldSetting(prev => ({ ...prev, worldDescription: e.target.value }))}
              placeholder="Describe the world, setting, atmosphere, key locations, and background information..."
              required
              disabled={streamingField === 'worldDescription'}
            />
          </CardContent>
        </Card>

        {/* Character Description */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              Your Character Description
              <AISparkButton field="characterDescription" />
            </CardTitle>
          </CardHeader>
          <CardContent>
            <AutoResizeTextarea
              value={streamingField === 'characterDescription' ? streamedValue : worldSetting.characterDescription}
              onChange={(e) => setWorldSetting(prev => ({ ...prev, characterDescription: e.target.value }))}
              placeholder="Describe your character's appearance, background, and current situation in this world..."
              required
              disabled={streamingField === 'characterDescription'}
            />
          </CardContent>
        </Card>

        {/* Character Role */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              Character Role
              <AISparkButton field="characterRole" />
            </CardTitle>
          </CardHeader>
          <CardContent>
            <AutoResizeTextarea
              value={streamingField === 'characterRole' ? streamedValue : worldSetting.characterRole}
              onChange={(e) => setWorldSetting(prev => ({ ...prev, characterRole: e.target.value }))}
              placeholder="Define your character's role, position, or significance within this world..."
              required
              disabled={streamingField === 'characterRole'}
            />
          </CardContent>
        </Card>

        {/* Submit Button */}
        <div className="flex justify-end space-x-4">
          <Button type="button" variant="outline" onClick={() => setSetupType('selection')}>
            Back
          </Button>
          <Button type="submit" disabled={isLoading}>
            {isLoading ? 'Creating...' : 'Start Chat'}
          </Button>
        </div>
      </form>
    </div>
  );

  // Show character creation/editing screen
  if (showCharacterCreation) {
    return (
      <CharacterCreation 
        onBack={handleCharacterCreationComplete}
        onPersonaCreated={handleCharacterCreationComplete}
        editingPersona={editingPersona}
      />
    );
  }

  return (
    <div className="min-h-screen bg-gradient-background p-8">
      {setupType === 'selection' && renderSetupSelection()}
      {setupType === 'character-selection' && renderCharacterSelection()}
      {setupType === 'character' && renderCharacterCreation()}
      {setupType === 'world' && renderWorldCreation()}
    </div>
  );
};

export default ChatSetup; 
