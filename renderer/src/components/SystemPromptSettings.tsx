import React, { useState, useEffect } from 'react';
import { But<PERSON> } from './ui/button';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { Textarea } from './ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Alert, AlertDescription } from './ui/alert';
import { Badge } from './ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { 
  AlertCircle, 
  Settings, 
  Plus, 
  Edit, 
  Trash2, 
  Check, 
  X, 
  Star,
  MessageSquare
} from 'lucide-react';

interface SystemPromptSetting {
  id: number;
  name: string;
  description: string;
  prompts: {
    character_generation?: string;
    ai_character?: string;
    ai_world?: string;
    choice_generation?: string;
  };
  is_default: boolean;
  created_at: string;
}

interface SystemPromptSettingsProps {
  onBack?: () => void;
}

const SystemPromptSettings: React.FC<SystemPromptSettingsProps> = ({ onBack }) => {
  const [settings, setSettings] = useState<SystemPromptSetting[]>([]);
  const [selectedSetting, setSelectedSetting] = useState<SystemPromptSetting | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [isEditing, setIsEditing] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [isEnhancing, setIsEnhancing] = useState(false);
  
  // Form states
  const [formName, setFormName] = useState('');
  const [formDescription, setFormDescription] = useState('');
  const [formPrompts, setFormPrompts] = useState({
    character_generation: '',
    ai_character: '',
    ai_world: '',
    choice_generation: ''
  });

  useEffect(() => {
    loadSystemPromptSettings();
  }, []);

  const loadSystemPromptSettings = async () => {
    try {
      const result = await window.electronAPI.getSystemPromptSettings();
      if (result.success) {
        setSettings(result.settings);
      }
    } catch (error) {
      console.error('Error loading system prompt settings:', error);
      setError('Failed to load system prompt settings');
    }
  };

  const handleSetDefault = async (id: number) => {
    try {
      const result = await window.electronAPI.setDefaultSystemPrompt(id);
      if (result.success) {
        await loadSystemPromptSettings();
      }
    } catch (error) {
      setError('Failed to set default system prompt');
    }
  };

  const handleDelete = async (id: number) => {
    if (!confirm('Are you sure you want to delete this system prompt setting?')) {
      return;
    }

    try {
      const result = await window.electronAPI.deleteSystemPromptSetting(id);
      if (result.success) {
        await loadSystemPromptSettings();
        if (selectedSetting?.id === id) {
          setSelectedSetting(null);
        }
      }
    } catch (error) {
      setError('Failed to delete system prompt setting');
    }
  };

  const handleEdit = (setting: SystemPromptSetting) => {
    setSelectedSetting(setting);
    setFormName(setting.name);
    setFormDescription(setting.description);
    setFormPrompts({
      character_generation: setting.prompts.character_generation || '',
      ai_character: setting.prompts.ai_character || '',
      ai_world: setting.prompts.ai_world || '',
      choice_generation: setting.prompts.choice_generation || ''
    });
    setIsEditing(true);
    setIsCreating(false);
  };

  const handleCreate = () => {
    setSelectedSetting(null);
    setFormName('');
    setFormDescription('');
    setFormPrompts({
      character_generation: '',
      ai_character: '',
      ai_world: '',
      choice_generation: ''
    });
    setIsCreating(true);
    setIsEditing(false);
  };

  const handleSave = async () => {
    if (!formName.trim() || !formDescription.trim()) {
      setError('Please fill in the name and description');
      return;
    }

    // Check if at least one prompt type has content
    const hasContent = Object.values(formPrompts).some(prompt => prompt.trim());
    if (!hasContent) {
      setError('Please fill in at least one prompt type');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      let result;
      if (isEditing && selectedSetting) {
        result = await window.electronAPI.updateSystemPromptSetting(
          selectedSetting.id,
          formName.trim(),
          formDescription.trim(),
          formPrompts
        );
      } else {
        result = await window.electronAPI.saveSystemPromptSetting(
          formName.trim(),
          formDescription.trim(),
          formPrompts,
          false
        );
      }

      if (result.success) {
        await loadSystemPromptSettings();
        setIsEditing(false);
        setIsCreating(false);
        setSelectedSetting(null);
        setFormName('');
        setFormDescription('');
        setFormPrompts({
          character_generation: '',
          ai_character: '',
          ai_world: '',
          choice_generation: ''
        });
      } else {
        setError(result.error || 'Failed to save system prompt setting');
      }
    } catch (err) {
      setError('Failed to save system prompt setting. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    setIsEditing(false);
    setIsCreating(false);
    setSelectedSetting(null);
    setFormName('');
    setFormDescription('');
    setFormPrompts({
      character_generation: '',
      ai_character: '',
      ai_world: '',
      choice_generation: ''
    });
    setError('');
  };

  const handleSelectSetting = (setting: SystemPromptSetting) => {
    setSelectedSetting(setting);
    setIsEditing(false);
    setIsCreating(false);
  };

  const handleEnhancePrompts = async () => {
    if (!formName.trim() || !formDescription.trim()) {
      setError('Please fill in the name and description first');
      return;
    }

    setIsEnhancing(true);
    setError('');

    try {
      const enhancePrompt = `You are an expert AI assistant helping to create system prompts for RPG AI interactions. 

Based on this information:
- Setting Name: "${formName}"
- Setting Description: "${formDescription}"

Create 4 specialized system prompts for different RPG AI functions. Each prompt should be well-crafted, detailed, and appropriate for the setting style described above.

You MUST respond with ONLY valid JSON in this exact format - no other text, no explanations:
{
  "character_generation": "Detailed prompt for generating character descriptions, personality, appearance, etc...",
  "ai_character": "Detailed prompt for how AI should act as a character in chats...",
  "ai_world": "Detailed prompt for how AI should manage and describe the world...",
  "choice_generation": "Detailed prompt for generating choices from the user's perspective..."
}

CRITICAL: Start your response with { and end with }. Do not include any text before or after the JSON. Make each prompt specific, detailed, and well-suited for the described setting style.`;

      const response = await window.electronAPI.sendToAI({
        message: enhancePrompt,
        promptType: 'character_generation' // Use character generation prompt for this meta-task
      });

      if (response.success) {
        try {
          const enhancedPrompts = JSON.parse(response.response);
          if (enhancedPrompts.character_generation && enhancedPrompts.ai_character && 
              enhancedPrompts.ai_world && enhancedPrompts.choice_generation) {
            setFormPrompts({
              character_generation: enhancedPrompts.character_generation,
              ai_character: enhancedPrompts.ai_character,
              ai_world: enhancedPrompts.ai_world,
              choice_generation: enhancedPrompts.choice_generation
            });
          } else {
            throw new Error('Invalid response format');
          }
        } catch (e) {
          console.error('Failed to parse enhanced prompts:', e);
          setError('Failed to parse enhanced prompts. Please try again.');
        }
      } else {
        setError(response.error || 'Failed to enhance prompts');
      }
    } catch (err) {
      console.error('Error enhancing prompts:', err);
      setError('Failed to enhance prompts. Please try again.');
    } finally {
      setIsEnhancing(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-background p-8">
      <Card className="w-full h-full">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Settings className="h-6 w-6 text-primary" />
                <div>
                  <CardTitle className="text-2xl">System Prompt Settings</CardTitle>
                  <CardDescription>
                    Configure how the AI responds to your requests
                  </CardDescription>
                </div>
              </div>
              {onBack && (
                <Button variant="outline" onClick={onBack}>
                  Back
                </Button>
              )}
            </div>
          </CardHeader>

          <CardContent className="space-y-8 p-8">
            {error && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <div className="grid grid-cols-1 xl:grid-cols-4 gap-8">
              {/* Left Panel - Settings List */}
              <div className="xl:col-span-1 space-y-6">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold">System Prompts</h3>
                  <Button size="sm" onClick={handleCreate}>
                    <Plus className="h-4 w-4 mr-2" />
                    New
                  </Button>
                </div>

                <div className="space-y-4 max-h-[calc(100vh-300px)] overflow-y-auto">
                  {settings.map((setting) => (
                    <Card
                      key={setting.id}
                      className={`cursor-pointer transition-colors ${
                        selectedSetting?.id === setting.id ? 'ring-2 ring-primary' : ''
                      }`}
                      onClick={() => handleSelectSetting(setting)}
                    >
                      <CardContent className="p-6">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-1">
                              <h4 className="font-medium">{setting.name}</h4>
                              {setting.is_default && (
                                <Badge variant="secondary" className="text-xs">
                                  <Star className="h-3 w-3 mr-1" />
                                  Default
                                </Badge>
                              )}
                            </div>
                            <p className="text-sm text-muted-foreground line-clamp-2">
                              {setting.description}
                            </p>
                          </div>
                          <div className="flex items-center gap-1 ml-2">
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleEdit(setting);
                              }}
                            >
                              <Edit className="h-3 w-3" />
                            </Button>
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDelete(setting.id);
                              }}
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>

              {/* Right Panel - Details/Edit */}
              <div className="xl:col-span-3">
                {isCreating || isEditing ? (
                  <Card>
                    <CardHeader>
                      <CardTitle>
                        {isCreating ? 'Create New System Prompt' : 'Edit System Prompt'}
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-6 p-6">
                      <div className="space-y-3">
                        <Label htmlFor="name">Name</Label>
                        <Input
                          id="name"
                          placeholder="e.g., Creative Assistant"
                          value={formName}
                          onChange={(e) => setFormName(e.target.value)}
                        />
                      </div>

                      <div className="space-y-3">
                        <Label htmlFor="description">Description</Label>
                        <Input
                          id="description"
                          placeholder="Brief description of this prompt style"
                          value={formDescription}
                          onChange={(e) => setFormDescription(e.target.value)}
                        />
                      </div>

                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <Label className="text-sm font-medium">Prompt Types</Label>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={handleEnhancePrompts}
                            disabled={isEnhancing}
                          >
                            {isEnhancing ? 'Enhancing...' : 'Enhance Prompts'}
                          </Button>
                        </div>
                        
                        <div className="space-y-4">
                          <div className="space-y-2">
                            <Label htmlFor="character-generation" className="text-sm">Character Generation</Label>
                            <Textarea
                              id="character-generation"
                              placeholder="Prompt for generating character descriptions, personality, appearance, etc..."
                              value={formPrompts.character_generation}
                              onChange={(e) => setFormPrompts(prev => ({ ...prev, character_generation: e.target.value }))}
                              rows={8}
                              className="min-h-[200px]"
                            />
                            <div className="text-xs text-muted-foreground text-right">
                              {formPrompts.character_generation.length} characters
                            </div>
                          </div>
                          
                          <div className="space-y-2">
                            <Label htmlFor="ai-character" className="text-sm">AI Character</Label>
                            <Textarea
                              id="ai-character"
                              placeholder="Prompt for how AI should act as a character in chats..."
                              value={formPrompts.ai_character}
                              onChange={(e) => setFormPrompts(prev => ({ ...prev, ai_character: e.target.value }))}
                              rows={8}
                              className="min-h-[200px]"
                            />
                            <div className="text-xs text-muted-foreground text-right">
                              {formPrompts.ai_character.length} characters
                            </div>
                          </div>
                          
                          <div className="space-y-2">
                            <Label htmlFor="ai-world" className="text-sm">AI World</Label>
                            <Textarea
                              id="ai-world"
                              placeholder="Prompt for how AI should manage and describe the world..."
                              value={formPrompts.ai_world}
                              onChange={(e) => setFormPrompts(prev => ({ ...prev, ai_world: e.target.value }))}
                              rows={8}
                              className="min-h-[200px]"
                            />
                            <div className="text-xs text-muted-foreground text-right">
                              {formPrompts.ai_world.length} characters
                            </div>
                          </div>
                          
                          <div className="space-y-2">
                            <Label htmlFor="choice-generation" className="text-sm">Choice Generation</Label>
                            <Textarea
                              id="choice-generation"
                              placeholder="Prompt for generating choices from the user's perspective..."
                              value={formPrompts.choice_generation}
                              onChange={(e) => setFormPrompts(prev => ({ ...prev, choice_generation: e.target.value }))}
                              rows={8}
                              className="min-h-[200px]"
                            />
                            <div className="text-xs text-muted-foreground text-right">
                              {formPrompts.choice_generation.length} characters
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="flex gap-4 pt-4">
                        <Button onClick={handleSave} disabled={isLoading}>
                          {isLoading ? 'Saving...' : 'Save'}
                        </Button>
                        <Button variant="outline" onClick={handleCancel}>
                          Cancel
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ) : selectedSetting ? (
                  <Card>
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <MessageSquare className="h-5 w-5" />
                          <CardTitle>{selectedSetting.name}</CardTitle>
                          {selectedSetting.is_default && (
                            <Badge variant="secondary">
                              <Star className="h-3 w-3 mr-1" />
                              Default
                            </Badge>
                          )}
                        </div>
                        <div className="flex gap-2">
                          <Button
                            size="sm"
                            onClick={() => handleSetDefault(selectedSetting.id)}
                            disabled={selectedSetting.is_default}
                          >
                            {selectedSetting.is_default ? (
                              <>
                                <Check className="h-4 w-4 mr-2" />
                                Default
                              </>
                            ) : (
                              <>
                                <Star className="h-4 w-4 mr-2" />
                                Set as Default
                              </>
                            )}
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleEdit(selectedSetting)}
                          >
                            <Edit className="h-4 w-4 mr-2" />
                            Edit
                          </Button>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-6 p-6">
                      <div className="space-y-3">
                        <Label className="text-sm font-medium">Description</Label>
                        <p className="text-sm text-muted-foreground">
                          {selectedSetting.description}
                        </p>
                      </div>

                      <div className="space-y-4">
                        <Label className="text-sm font-medium">Prompt Types</Label>
                        
                        <div className="space-y-4">
                          {selectedSetting.prompts.character_generation && (
                            <div className="space-y-2">
                              <Label className="text-sm font-medium">Character Generation</Label>
                              <div className="p-4 bg-muted rounded-lg">
                                <p className="text-sm whitespace-pre-wrap leading-relaxed">
                                  {selectedSetting.prompts.character_generation}
                                </p>
                              </div>
                            </div>
                          )}
                          
                          {selectedSetting.prompts.ai_character && (
                            <div className="space-y-2">
                              <Label className="text-sm font-medium">AI Character</Label>
                              <div className="p-4 bg-muted rounded-lg">
                                <p className="text-sm whitespace-pre-wrap leading-relaxed">
                                  {selectedSetting.prompts.ai_character}
                                </p>
                              </div>
                            </div>
                          )}
                          
                          {selectedSetting.prompts.ai_world && (
                            <div className="space-y-2">
                              <Label className="text-sm font-medium">AI World</Label>
                              <div className="p-4 bg-muted rounded-lg">
                                <p className="text-sm whitespace-pre-wrap leading-relaxed">
                                  {selectedSetting.prompts.ai_world}
                                </p>
                              </div>
                            </div>
                          )}
                          
                          {selectedSetting.prompts.choice_generation && (
                            <div className="space-y-2">
                              <Label className="text-sm font-medium">Choice Generation</Label>
                              <div className="p-4 bg-muted rounded-lg">
                                <p className="text-sm whitespace-pre-wrap leading-relaxed">
                                  {selectedSetting.prompts.choice_generation}
                                </p>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>

                      <div className="text-xs text-muted-foreground pt-2">
                        Created: {new Date(selectedSetting.created_at).toLocaleDateString()}
                      </div>
                    </CardContent>
                  </Card>
                ) : (
                  <Card>
                    <CardContent className="flex flex-col items-center justify-center py-16 text-center">
                      <MessageSquare className="h-16 w-16 text-muted-foreground mb-6" />
                      <h3 className="text-xl font-medium mb-3">No System Prompt Selected</h3>
                      <p className="text-muted-foreground mb-6 max-w-md">
                        Select a system prompt from the list to view its details, or create a new one.
                      </p>
                      <Button onClick={handleCreate} size="lg">
                        <Plus className="h-5 w-5 mr-2" />
                        Create New System Prompt
                      </Button>
                    </CardContent>
                  </Card>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
    </div>
  );
};

export default SystemPromptSettings; 