import React, { useState } from 'react';
import { User } from 'lucide-react';
import { DraggableWindow } from './draggable-window';

interface PersonaImageProps {
  image?: string;
  name: string;
  position: 'bottom-left' | 'bottom-right';
  className?: string;
}

export const PersonaImage: React.FC<PersonaImageProps> = ({
  image,
  name,
  position,
  className = ''
}) => {
  const [isWindowOpen, setIsWindowOpen] = useState(false);

  const handleImageClick = () => {
    setIsWindowOpen(true);
  };

  const handleCloseWindow = () => {
    setIsWindowOpen(false);
  };

  const positionClasses = {
    'bottom-left': 'bottom-4 left-4',
    'bottom-right': 'bottom-4 right-4'
  };

  return (
    <>
      {/* Clickable image in corner */}
      <div
        className={`fixed ${positionClasses[position]} z-40 cursor-pointer transition-all duration-200 hover:scale-110 ${className}`}
        onClick={handleImageClick}
        title={`Click to view ${name}`}
      >
        <div className="relative">
          {image ? (
            <img
              src={image}
              alt={name}
              className="w-16 h-16 object-contain border-2 border-border shadow-lg hover:border-accent transition-colors duration-200 bg-background"
            />
          ) : (
            <div className="w-16 h-16 bg-gradient-to-br from-blue-100 to-blue-200 flex items-center justify-center border-2 border-border shadow-lg hover:border-accent transition-colors duration-200">
              <User className="w-8 h-8 text-blue-600" />
            </div>
          )}
          {/* Hover indicator */}
          <div className="absolute inset-0 bg-black/0 hover:bg-black/10 transition-colors duration-200 flex items-center justify-center">
            <span className="text-white text-xs font-medium opacity-0 hover:opacity-100 transition-opacity duration-200">
              View
            </span>
          </div>
        </div>
      </div>

      {/* Draggable window */}
      {isWindowOpen && (
        <DraggableWindow
          onClose={handleCloseWindow}
          initialPosition={{
            x: position === 'bottom-left' ? 50 : window.innerWidth - 350,
            y: window.innerHeight - 450
          }}
          initialSize={{ width: 400, height: 500 }}
          minWidth={300}
          minHeight={400}
          maxWidth={800}
          maxHeight={800}
        >
          <div className="flex justify-center items-center h-full">
            {image ? (
              <img
                src={image}
                alt={name}
                className="max-w-full max-h-full object-contain"
              />
            ) : (
              <div className="w-32 h-32 bg-gradient-to-br from-blue-100 to-blue-200 flex items-center justify-center border-2 border-border shadow-lg">
                <User className="w-16 h-16 text-blue-600" />
              </div>
            )}
          </div>
        </DraggableWindow>
      )}
    </>
  );
}; 