import React, { useState, useRef, useEffect } from 'react';
import { X } from 'lucide-react';

interface DraggableWindowProps {
  children: React.ReactNode;
  onClose: () => void;
  initialPosition?: { x: number; y: number };
  initialSize?: { width: number; height: number };
  minWidth?: number;
  minHeight?: number;
  maxWidth?: number;
  maxHeight?: number;
}

export const DraggableWindow: React.FC<DraggableWindowProps> = ({
  children,
  onClose,
  initialPosition = { x: 100, y: 100 },
  initialSize = { width: 300, height: 300 },
  minWidth = 150,
  minHeight = 150,
  maxWidth = 1000,
  maxHeight = 800
}) => {
  const [position, setPosition] = useState(initialPosition);
  const [size, setSize] = useState(initialSize);
  const [isDragging, setIsDragging] = useState(false);
  const [isResizing, setIsResizing] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });

  const headerRef = useRef<HTMLDivElement>(null);

  // Start dragging
  const handleMouseDown = (e: React.MouseEvent) => {
    e.preventDefault();
    const rect = headerRef.current?.getBoundingClientRect();
    if (!rect) return;

    setDragOffset({
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    });
    setIsDragging(true);
  };

  // Start resizing
  const handleResizeMouseDown = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsResizing(true);
  };

  // Handle mouse move for dragging/resizing
  useEffect(() => {
    const onMouseMove = (e: MouseEvent) => {
      if (isDragging) {
        setPosition({
          x: e.clientX - dragOffset.x,
          y: e.clientY - dragOffset.y
        });
      } else if (isResizing) {
        const newWidth = Math.max(minWidth, Math.min(maxWidth, e.clientX - position.x));
        const newHeight = Math.max(minHeight, Math.min(maxHeight, e.clientY - position.y));
        setSize({ width: newWidth, height: newHeight });
      }
    };

    const onMouseUp = () => {
      setIsDragging(false);
      setIsResizing(false);
    };

    if (isDragging || isResizing) {
      document.addEventListener('mousemove', onMouseMove);
      document.addEventListener('mouseup', onMouseUp);
    }

    return () => {
      document.removeEventListener('mousemove', onMouseMove);
      document.removeEventListener('mouseup', onMouseUp);
    };
  }, [isDragging, isResizing, dragOffset, position, minWidth, minHeight, maxWidth, maxHeight]);

  return (
    <div
      className={`fixed z-50 bg-white border shadow-lg ${isDragging || isResizing ? 'select-none' : ''}`}
      style={{
        left: position.x,
        top: position.y,
        width: size.width,
        height: size.height,
        userSelect: isDragging || isResizing ? 'none' : 'auto',
        WebkitUserSelect: isDragging || isResizing ? 'none' : 'auto',
        MozUserSelect: isDragging || isResizing ? 'none' : 'auto',
        msUserSelect: isDragging || isResizing ? 'none' : undefined
      }}
    >
      {/* Header */}
      <div
        ref={headerRef}
        onMouseDown={handleMouseDown}
        className="h-6 bg-gray-800 text-white flex justify-end items-center px-1 cursor-move"
      >
        <button
          onClick={onClose}
          className="h-4 w-4 flex items-center justify-center text-sm text-white hover:text-red-400"
        >
          <X className="w-3 h-3" />
        </button>
      </div>

      {/* Content */}
      <div className="w-full h-[calc(100%-1.5rem)] overflow-hidden">{children}</div>

      {/* Bottom-right resize handle */}
      <div
        className="absolute bottom-0 right-0 w-3 h-3 cursor-se-resize"
        onMouseDown={handleResizeMouseDown}
      />
    </div>
  );
};
