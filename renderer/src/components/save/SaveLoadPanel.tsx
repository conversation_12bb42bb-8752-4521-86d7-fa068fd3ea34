import React, { useState, useEffect } from 'react';
import { Card, CardHeader, CardTitle, CardContent } from '../ui/card';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Badge } from '../ui/badge';
import { ScrollArea } from '../ui/scroll-area';
import { X, Save, Download, Trash2, Clock } from 'lucide-react';
import { cn } from '@/lib/utils';

interface SaveData {
  id: string;
  name: string;
  timestamp: number;
  chapter: string;
  characterLevel: number;
  characterName: string;
  data: any;
}

interface SaveLoadPanelProps {
  onSave: () => void;
  onLoad: () => void;
  onClose: () => void;
}

export const SaveLoadPanel: React.FC<SaveLoadPanelProps> = ({
  onSave,
  onLoad,
  onClose
}) => {
  const [saves, setSaves] = useState<SaveData[]>([]);
  const [newSaveName, setNewSaveName] = useState('');

  useEffect(() => {
    loadSaveFiles();
  }, []);

  const loadSaveFiles = () => {
    const savedGames = localStorage.getItem('rpgChatSaves');
    if (savedGames) {
      try {
        setSaves(JSON.parse(savedGames));
      } catch (error) {
        console.error('Failed to load save files:', error);
      }
    }
  };

  const createSave = () => {
    if (!newSaveName.trim()) return;

    // Get current game data from localStorage
    const currentGameData = localStorage.getItem('rpgChatSave');
    if (!currentGameData) return;

    const gameData = JSON.parse(currentGameData);
    const newSave: SaveData = {
      id: Date.now().toString(),
      name: newSaveName.trim(),
      timestamp: Date.now(),
      chapter: gameData.gameState?.chapter || 'Unknown Chapter',
      characterLevel: gameData.character?.level || 1,
      characterName: gameData.character?.name || 'Adventurer',
      data: gameData
    };

    const updatedSaves = [newSave, ...saves].slice(0, 10); // Keep only 10 saves
    setSaves(updatedSaves);
    localStorage.setItem('rpgChatSaves', JSON.stringify(updatedSaves));
    setNewSaveName('');
    onSave();
  };

  const loadSave = (saveData: SaveData) => {
    localStorage.setItem('rpgChatSave', JSON.stringify(saveData.data));
    onLoad();
    onClose();
  };

  const deleteSave = (saveId: string) => {
    const updatedSaves = saves.filter(save => save.id !== saveId);
    setSaves(updatedSaves);
    localStorage.setItem('rpgChatSaves', JSON.stringify(updatedSaves));
  };

  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp).toLocaleString([], {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="fixed inset-0 z-50 bg-background/80 backdrop-blur-sm flex items-center justify-center p-4">
      <Card className="w-full max-w-2xl max-h-[80vh] bg-gradient-card shadow-glow border-primary/20">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
          <CardTitle className="text-xl font-bold">Save & Load Game</CardTitle>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Quick Save Section */}
          <div className="space-y-3">
            <h3 className="font-semibold flex items-center gap-2">
              <Save className="h-4 w-4" />
              Create New Save
            </h3>
            <div className="flex gap-2">
              <Input
                placeholder="Enter save name..."
                value={newSaveName}
                onChange={(e) => setNewSaveName(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && createSave()}
                className="flex-1"
              />
              <Button 
                onClick={createSave}
                disabled={!newSaveName.trim()}
                className="bg-gradient-primary hover:shadow-primary"
              >
                <Save className="h-4 w-4 mr-2" />
                Save
              </Button>
            </div>
          </div>

          {/* Save Files List */}
          <div className="space-y-3">
            <h3 className="font-semibold flex items-center gap-2">
              <Download className="h-4 w-4" />
              Saved Games ({saves.length}/10)
            </h3>
            
            <ScrollArea className="h-64 border border-border rounded-lg">
              {saves.length === 0 ? (
                <div className="p-4 text-center text-muted-foreground">
                  No saved games found
                </div>
              ) : (
                <div className="p-2 space-y-2">
                  {saves.map((save) => (
                    <div
                      key={save.id}
                      className="border border-border rounded-lg p-3 hover:bg-muted/50 transition-colors"
                    >
                      <div className="flex items-start justify-between gap-3">
                        <div className="flex-1 space-y-1">
                          <h4 className="font-medium text-foreground">{save.name}</h4>
                          <div className="flex items-center gap-2 text-sm text-muted-foreground">
                            <Badge variant="outline" className="text-xs">
                              {save.characterName} - Level {save.characterLevel}
                            </Badge>
                            <span>•</span>
                            <span>{save.chapter}</span>
                          </div>
                          <div className="flex items-center gap-1 text-xs text-muted-foreground">
                            <Clock className="h-3 w-3" />
                            {formatTimestamp(save.timestamp)}
                          </div>
                        </div>
                        
                        <div className="flex gap-1">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => loadSave(save)}
                            className="hover:bg-accent/10 hover:border-accent"
                          >
                            <Download className="h-3 w-3 mr-1" />
                            Load
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => deleteSave(save.id)}
                            className="hover:bg-destructive/10 hover:border-destructive text-destructive"
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </ScrollArea>
          </div>

          {/* Quick Actions */}
          <div className="flex justify-between pt-4 border-t border-border">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <div className="flex gap-2">
              <Button 
                variant="outline" 
                onClick={onSave}
                className="hover:bg-accent/10 hover:border-accent"
              >
                <Save className="h-4 w-4 mr-2" />
                Quick Save
              </Button>
              <Button 
                variant="outline" 
                onClick={onLoad}
                className="hover:bg-secondary/10 hover:border-secondary"
              >
                <Download className="h-4 w-4 mr-2" />
                Quick Load
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};