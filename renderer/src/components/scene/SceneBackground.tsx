import React from 'react';
import { cn } from '@/lib/utils';

interface SceneBackgroundProps {
  scene: string;
  mood: 'light' | 'neutral' | 'dark' | 'mysterious' | 'combat';
  className?: string;
}

export const SceneBackground: React.FC<SceneBackgroundProps> = ({
  scene,
  mood,
  className
}) => {
  const getSceneGradient = (scene: string, mood: string) => {
    const scenes = {
      forest: {
        light: 'from-green-900/20 via-green-800/10 to-emerald-900/20',
        neutral: 'from-green-900/30 via-slate-900/20 to-emerald-900/30',
        dark: 'from-gray-900/40 via-green-900/30 to-black/40',
        mysterious: 'from-purple-900/30 via-indigo-900/20 to-purple-800/30',
        combat: 'from-red-900/30 via-orange-900/20 to-red-800/30'
      },
      cottage: {
        light: 'from-amber-900/20 via-orange-800/10 to-yellow-900/20',
        neutral: 'from-amber-900/30 via-brown-800/20 to-orange-900/30',
        dark: 'from-gray-900/40 via-brown-900/30 to-black/40',
        mysterious: 'from-purple-900/30 via-amber-900/20 to-indigo-800/30',
        combat: 'from-red-900/30 via-orange-900/20 to-red-800/30'
      },
      dungeon: {
        light: 'from-slate-800/20 via-gray-700/10 to-stone-800/20',
        neutral: 'from-slate-900/30 via-gray-800/20 to-stone-900/30',
        dark: 'from-black/50 via-gray-900/40 to-black/50',
        mysterious: 'from-purple-900/40 via-violet-900/30 to-indigo-900/40',
        combat: 'from-red-900/40 via-crimson-900/30 to-red-800/40'
      },
      tavern: {
        light: 'from-amber-800/20 via-yellow-700/10 to-orange-800/20',
        neutral: 'from-amber-900/30 via-brown-800/20 to-orange-900/30',
        dark: 'from-brown-900/40 via-amber-900/30 to-black/40',
        mysterious: 'from-purple-800/30 via-amber-900/20 to-violet-800/30',
        combat: 'from-red-900/30 via-orange-900/20 to-red-800/30'
      }
    };

    const sceneData = scenes[scene as keyof typeof scenes] || scenes.forest;
    return sceneData[mood as keyof typeof sceneData] || sceneData.neutral;
  };

  const getScenePattern = (scene: string) => {
    switch (scene) {
      case 'forest':
        return (
          <div className="absolute inset-0 opacity-10">
            <div className="absolute top-10 left-10 text-6xl">🌲</div>
            <div className="absolute top-32 right-20 text-4xl">🍃</div>
            <div className="absolute bottom-20 left-1/4 text-5xl">🌿</div>
            <div className="absolute top-20 right-1/3 text-3xl">🦋</div>
          </div>
        );
      case 'cottage':
        return (
          <div className="absolute inset-0 opacity-10">
            <div className="absolute top-20 right-20 text-5xl">🏠</div>
            <div className="absolute bottom-32 left-16 text-3xl">🌼</div>
            <div className="absolute top-40 left-1/3 text-4xl">💨</div>
          </div>
        );
      case 'dungeon':
        return (
          <div className="absolute inset-0 opacity-10">
            <div className="absolute top-16 left-16 text-4xl">🗿</div>
            <div className="absolute bottom-24 right-24 text-3xl">⚱️</div>
            <div className="absolute top-32 right-32 text-3xl">🕯️</div>
            <div className="absolute bottom-16 left-1/3 text-2xl">💀</div>
          </div>
        );
      case 'tavern':
        return (
          <div className="absolute inset-0 opacity-10">
            <div className="absolute top-24 right-16 text-4xl">🍺</div>
            <div className="absolute bottom-32 left-20 text-3xl">🔥</div>
            <div className="absolute top-16 left-1/3 text-3xl">🎵</div>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className={cn(
      "absolute inset-0 bg-gradient-to-br transition-all duration-1000",
      getSceneGradient(scene, mood),
      className
    )}>
      {getScenePattern(scene)}
      
      {/* Atmospheric overlay */}
      <div className={cn(
        "absolute inset-0 transition-opacity duration-1000",
        mood === 'mysterious' && "bg-purple-500/5",
        mood === 'dark' && "bg-black/20",
        mood === 'combat' && "bg-red-500/10 animate-pulse",
        mood === 'light' && "bg-yellow-200/5"
      )} />
      
      {/* Subtle animated particles for atmosphere */}
      {mood === 'mysterious' && (
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-1/4 left-1/4 w-1 h-1 bg-purple-400 rounded-full animate-ping" style={{ animationDelay: '0s' }} />
          <div className="absolute top-3/4 right-1/3 w-1 h-1 bg-indigo-400 rounded-full animate-ping" style={{ animationDelay: '1s' }} />
          <div className="absolute bottom-1/4 left-2/3 w-1 h-1 bg-violet-400 rounded-full animate-ping" style={{ animationDelay: '2s' }} />
        </div>
      )}
    </div>
  );
};