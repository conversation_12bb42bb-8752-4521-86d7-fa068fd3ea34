import React from 'react';
import { Character, GameState } from '../RPGChat';
import { <PERSON>, <PERSON>Header, CardT<PERSON>le, CardContent } from '../ui/card';
import { Progress } from '../ui/progress';
import { Badge } from '../ui/badge';
import { Separator } from '../ui/separator';
import { ScrollArea } from '../ui/scroll-area';
import { Button } from '../ui/button';
import { X, Heart, Shield, Zap, Star, Package } from 'lucide-react';
import { cn } from '@/lib/utils';

interface CharacterSheetProps {
  character: Character;
  gameState: GameState;
  onClose: () => void;
}

export const CharacterSheet: React.FC<CharacterSheetProps> = ({
  character,
  gameState,
  onClose
}) => {
  const getStatIcon = (stat: string) => {
    const icons = {
      strength: '💪',
      charm: '💫',
      arcana: '🔮',
      luck: '🍀'
    };
    return icons[stat as keyof typeof icons] || '⭐';
  };

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'legendary': return 'text-accent';
      case 'rare': return 'text-inventory-rare';
      case 'uncommon': return 'text-secondary';
      default: return 'text-inventory-common';
    }
  };

  const getRelationshipColor = (level: number) => {
    if (level >= 50) return 'text-stat-positive';
    if (level <= -50) return 'text-stat-negative';
    return 'text-muted-foreground';
  };

  const experienceToNext = (character.level * 100) - character.experience;
  const experienceProgress = (character.experience % 100);

  return (
    <div className="h-full flex flex-col bg-gradient-card">
      <div className="flex items-center justify-between p-4 border-b border-border">
        <h2 className="text-xl font-bold text-foreground">Character Sheet</h2>
        <Button variant="ghost" size="sm" onClick={onClose}>
          <X className="h-4 w-4" />
        </Button>
      </div>

      <ScrollArea className="flex-1 p-4">
        <div className="space-y-6">
          {/* Character Info */}
          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-center gap-3">
                <div className="text-3xl">{character.avatar}</div>
                <div>
                  <CardTitle className="text-lg">{character.name}</CardTitle>
                  <p className="text-sm text-muted-foreground">Level {character.level} Adventurer</p>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Experience</span>
                  <span>{character.experience} XP</span>
                </div>
                <Progress value={experienceProgress} className="h-2" />
                <p className="text-xs text-muted-foreground text-center">
                  {experienceToNext} XP to next level
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Stats */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5" />
                Attributes
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                {Object.entries(character.stats).map(([stat, value]) => (
                  <div key={stat} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium capitalize flex items-center gap-1">
                        <span>{getStatIcon(stat)}</span>
                        {stat}
                      </span>
                      <Badge 
                        variant="secondary" 
                        className={cn(
                          "animate-stat-pulse",
                          value >= 15 ? "bg-stat-positive/20 text-stat-positive" : 
                          value <= 8 ? "bg-stat-negative/20 text-stat-negative" : ""
                        )}
                      >
                        {value}
                      </Badge>
                    </div>
                    <Progress value={(value / 20) * 100} className="h-1" />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Inventory */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                Inventory
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {character.inventory.map((item) => (
                  <div key={item.id} className="border border-border rounded-lg p-3 hover:bg-muted/50 transition-colors">
                    <div className="flex items-start justify-between gap-2">
                      <div className="flex-1">
                        <h4 className={cn("font-medium", getRarityColor(item.rarity))}>
                          {item.name}
                        </h4>
                        <p className="text-xs text-muted-foreground mt-1">
                          {item.description}
                        </p>
                        {item.effects && (
                          <div className="mt-2 space-y-1">
                            {item.effects.map((effect, index) => (
                              <Badge key={index} variant="outline" className="text-xs">
                                {effect}
                              </Badge>
                            ))}
                          </div>
                        )}
                      </div>
                      <Badge variant="secondary" className="capitalize">
                        {item.type}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Relationships */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Heart className="h-5 w-5" />
                Relationships
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {character.relationships.map((rel, index) => (
                  <div key={index} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="font-medium">{rel.characterName}</span>
                      <Badge 
                        variant="outline"
                        className={getRelationshipColor(rel.level)}
                      >
                        {rel.status}
                      </Badge>
                    </div>
                    <div className="flex items-center gap-2">
                      <Progress 
                        value={((rel.level + 100) / 200) * 100} 
                        className="h-2 flex-1"
                      />
                      <span className={cn("text-xs", getRelationshipColor(rel.level))}>
                        {rel.level > 0 ? '+' : ''}{rel.level}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Current Quest */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Quest Log
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Badge variant="outline" className="mb-2">
                  {gameState.chapter}
                </Badge>
                {gameState.questLog.map((quest, index) => (
                  <div key={index} className="flex items-center gap-2 text-sm">
                    <Star className="h-3 w-3 text-accent" />
                    <span>{quest}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </ScrollArea>
    </div>
  );
};