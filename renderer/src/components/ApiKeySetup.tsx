import React, { useState, useEffect } from 'react';
import { But<PERSON> } from './ui/button';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Alert, AlertDescription } from './ui/alert';
import { AlertCircle, Key, ExternalLink, Plus, Edit, Trash2, Check, X, Settings, MessageSquare } from 'lucide-react';
import { Badge } from './ui/badge';

interface ApiKey {
  id: number;
  name: string;
  api_key: string;
  is_active: boolean;
  created_at: string;
  last_used?: string;
}

interface ApiKeySetupProps {
  onApiKeySet: () => void;
  onBack?: () => void;
  onShowSystemPrompts?: () => void;
}

const ApiKeySetup: React.FC<ApiKeySetupProps> = ({ onApiKeySet, onBack, onShowSystemPrompts }) => {
  const [apiKeys, setApiKeys] = useState<ApiKey[]>([]);
  const [newApiKey, setNewApiKey] = useState('');
  const [newApiKeyName, setNewApiKeyName] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [isValidating, setIsValidating] = useState(false);
  const [editingKey, setEditingKey] = useState<number | null>(null);
  const [editingName, setEditingName] = useState('');

  useEffect(() => {
    loadApiKeys();
  }, []);

  const loadApiKeys = async () => {
    try {
      const result = await window.electronAPI.getApiKeys();
      if (result.success) {
        setApiKeys(result.apiKeys);
      }
    } catch (error) {
      console.error('Error loading API keys:', error);
    }
  };

  const handleSaveApiKey = async () => {
    if (!newApiKey.trim() || !newApiKeyName.trim()) {
      setError('Please enter both a name and API key');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      const result = await window.electronAPI.saveApiKey(newApiKey.trim(), newApiKeyName.trim(), true);
      if (result.success) {
        setNewApiKey('');
        setNewApiKeyName('');
        await loadApiKeys();
        onApiKeySet();
      } else {
        setError(result.error || 'Failed to save API key');
      }
    } catch (err) {
      setError('Failed to save API key. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleTestApiKey = async () => {
    if (!newApiKey.trim()) {
      setError('Please enter your OpenRouter API key first');
      return;
    }

    setIsValidating(true);
    setError('');

    try {
      const result = await window.electronAPI.testApiKey(newApiKey.trim());
      
      if (result.success) {
        setError('');
        // API key is valid, save it
        await handleSaveApiKey();
      } else {
        setError('Invalid API key. Please check your OpenRouter API key and try again.');
      }
    } catch (err) {
      setError('Failed to validate API key. Please check your connection and try again.');
    } finally {
      setIsValidating(false);
    }
  };

  const handleSetActive = async (id: number) => {
    try {
      const result = await window.electronAPI.setActiveApiKey(id);
      if (result.success) {
        await loadApiKeys();
        onApiKeySet();
      }
    } catch (error) {
      setError('Failed to set active API key');
    }
  };

  const handleDeleteApiKey = async (id: number) => {
    try {
      const result = await window.electronAPI.deleteApiKey(id);
      if (result.success) {
        await loadApiKeys();
      }
    } catch (error) {
      setError('Failed to delete API key');
    }
  };

  const handleEditName = async (id: number) => {
    try {
      const result = await window.electronAPI.updateApiKeyName(id, editingName);
      if (result.success) {
        setEditingKey(null);
        setEditingName('');
        await loadApiKeys();
      }
    } catch (error) {
      setError('Failed to update API key name');
    }
  };

  const startEditing = (apiKey: ApiKey) => {
    setEditingKey(apiKey.id);
    setEditingName(apiKey.name);
  };

  const cancelEditing = () => {
    setEditingKey(null);
    setEditingName('');
  };

  const openOpenRouterWebsite = () => {
    window.open('https://openrouter.ai/keys', '_blank');
  };

  return (
    <div className="min-h-screen bg-gradient-background p-8">
      <Card className="w-full h-full">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-primary/10">
            <Key className="h-6 w-6 text-primary" />
          </div>
          <CardTitle className="text-2xl">API Key Management</CardTitle>
          <CardDescription>
            Manage your OpenRouter API keys
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-8 p-8">
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Saved API Keys */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Saved API Keys</h3>
            {apiKeys.length === 0 ? (
              <p className="text-muted-foreground text-center py-4">No API keys saved yet.</p>
            ) : (
              <div className="space-y-4">
                {apiKeys.map((apiKey) => (
                  <div key={apiKey.id} className="flex items-center justify-between bg-background rounded-lg p-6 border border-border">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        {editingKey === apiKey.id ? (
                          <div className="flex items-center gap-2">
                            <Input
                              value={editingName}
                              onChange={(e) => setEditingName(e.target.value)}
                              className="w-32"
                            />
                            <Button size="sm" onClick={() => handleEditName(apiKey.id)}>
                              <Check className="h-3 w-3" />
                            </Button>
                            <Button size="sm" variant="outline" onClick={cancelEditing}>
                              <X className="h-3 w-3" />
                            </Button>
                          </div>
                        ) : (
                          <span className="font-medium">{apiKey.name}</span>
                        )}
                        {apiKey.is_active && (
                          <Badge variant="default" className="text-xs">Active</Badge>
                        )}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        Created: {new Date(apiKey.created_at).toLocaleDateString()}
                        {apiKey.last_used && ` • Last used: ${new Date(apiKey.last_used).toLocaleDateString()}`}
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      {!apiKey.is_active && (
                        <Button 
                          size="sm" 
                          variant="outline"
                          onClick={() => handleSetActive(apiKey.id)}
                        >
                          Set Active
                        </Button>
                      )}
                      <Button 
                        size="sm" 
                        variant="outline"
                        onClick={() => startEditing(apiKey)}
                      >
                        <Edit className="h-3 w-3" />
                      </Button>
                      <Button 
                        size="sm" 
                        variant="outline"
                        onClick={() => handleDeleteApiKey(apiKey.id)}
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Add New API Key */}
          <div className="space-y-4 border-t pt-6">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Add New API Key
            </h3>
            
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="api-key-name">Name</Label>
                <Input
                  id="api-key-name"
                  placeholder="e.g., Personal Key"
                  value={newApiKeyName}
                  onChange={(e) => setNewApiKeyName(e.target.value)}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="api-key">OpenRouter API Key</Label>
                <Input
                  id="api-key"
                  type="password"
                  placeholder="sk-or-v1-..."
                  value={newApiKey}
                  onChange={(e) => setNewApiKey(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      handleTestApiKey();
                    }
                  }}
                />
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              <Button 
                onClick={handleTestApiKey} 
                disabled={isLoading || isValidating}
                className="w-full"
              >
                {isValidating ? 'Validating...' : 'Test & Save API Key'}
              </Button>
              
              <Button 
                variant="outline" 
                onClick={openOpenRouterWebsite}
                className="w-full"
              >
                <ExternalLink className="mr-2 h-4 w-4" />
                Get API Key from OpenRouter
              </Button>
            </div>
          </div>

          {/* System Prompt Settings */}
          <div className="space-y-4 border-t pt-6">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <MessageSquare className="h-4 w-4" />
              System Prompt Settings
            </h3>
            <p className="text-sm text-muted-foreground">
              Configure how the AI responds to your requests. Choose from predefined styles or create custom prompts.
            </p>
            <Button 
              variant="outline" 
              onClick={onShowSystemPrompts}
              className="w-full"
            >
              <Settings className="mr-2 h-4 w-4" />
              Manage System Prompts
            </Button>
          </div>

          <div className="text-xs text-muted-foreground text-center space-y-1">
            <p>Don't have an API key?</p>
            <p>
              Visit{' '}
              <button 
                onClick={openOpenRouterWebsite}
                className="text-primary hover:underline"
              >
                openrouter.ai
              </button>
              {' '}to create a free account and get your API key.
            </p>
          </div>

          {onBack && (
            <div className="flex justify-center pt-4">
              <Button variant="outline" onClick={onBack}>
                Back to Directory
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default ApiKeySetup; 