const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Initialize bootstrap holder on window to allow renderer to read initial chat content/options
// Structure: window.__ELECTROBOX_BOOTSTRAP__ = { initialChatBySetting: { 'character:123': { content, options }, 'world:456': { content, options } } }
(function ensureBootstrap() {
  if (!globalThis.__ELECTROBOX_BOOTSTRAP__) {
    Object.defineProperty(globalThis, '__ELECTROBOX_BOOTSTRAP__', {
      value: { initialChatBySetting: {} },
      writable: false,
      configurable: false,
      enumerable: false
    });
  }
})();

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // Database operations
  saveMessage: (message) => ipcRenderer.invoke('save-message', message),
  getMessages: () => ipcRenderer.invoke('get-messages'),
  clearMessages: () => ipcRenderer.invoke('clear-messages'),
  
  // AI API operations
  sendToAI: (message) => ipcRenderer.invoke('send-to-ai', message),
  
  // Settings operations
  saveApiKey: (apiKey, name, setActive) => ipcRenderer.invoke('save-api-key', apiKey, name, setActive),
  getApiKeys: () => ipcRenderer.invoke('get-api-keys'),
  getActiveApiKey: () => ipcRenderer.invoke('get-active-api-key'),
  setActiveApiKey: (id) => ipcRenderer.invoke('set-active-api-key', id),
  deleteApiKey: (id) => ipcRenderer.invoke('delete-api-key', id),
  updateApiKeyName: (id, name) => ipcRenderer.invoke('update-api-key-name', id, name),
  getApiKey: () => ipcRenderer.invoke('get-api-key'),
  testApiKey: (apiKey) => ipcRenderer.invoke('test-api-key', apiKey),
  
  // System prompt settings
  getSystemPromptSettings: () => ipcRenderer.invoke('get-system-prompt-settings'),
  getDefaultSystemPrompt: () => ipcRenderer.invoke('get-default-system-prompt'),
  setDefaultSystemPrompt: (id) => ipcRenderer.invoke('set-default-system-prompt', id),
  saveSystemPromptSetting: (name, description, prompt, isDefault) => ipcRenderer.invoke('save-system-prompt-setting', name, description, prompt, isDefault),
  updateSystemPromptSetting: (id, name, description, prompt) => ipcRenderer.invoke('update-system-prompt-setting', id, name, description, prompt),
  deleteSystemPromptSetting: (id) => ipcRenderer.invoke('delete-system-prompt-setting', id),
  
  // App info
  getAppVersion: () => ipcRenderer.invoke('get-app-version'),
 
  // Character settings
  saveCharacterSetting: (characterSetting) => ipcRenderer.invoke('save-character-setting', characterSetting),
  getCharacterSettings: () => ipcRenderer.invoke('get-character-settings'),
  getCharacterSetting: (id) => ipcRenderer.invoke('get-character-setting', id),

  // World settings
  saveWorldSetting: (worldSetting) => ipcRenderer.invoke('save-world-setting', worldSetting),
  getWorldSettings: () => ipcRenderer.invoke('get-world-settings'),
  getWorldSetting: (id) => ipcRenderer.invoke('get-world-setting', id),

  // Chats
  saveChat: (parentId, settingId, settingType, contents, userPersonaId, characterId) => ipcRenderer.invoke('save-chat', parentId, settingId, settingType, contents, userPersonaId, characterId),
  updateChat: (chatId, contents) => ipcRenderer.invoke('update-chat', chatId, contents),
  getChats: (settingId, settingType) => ipcRenderer.invoke('get-chats', settingId, settingType),
  getRecentChats: (limit) => ipcRenderer.invoke('get-recent-chats', limit),
  getAllChats: (limit, offset) => ipcRenderer.invoke('get-all-chats', limit, offset),
  getDirectoryChats: (limit) => ipcRenderer.invoke('get-directory-chats', limit),
  getConversationTree: (rootChatId) => ipcRenderer.invoke('get-conversation-tree', rootChatId),
  
  // Persona creation
  savePersona: (persona) => ipcRenderer.invoke('save-persona', persona),
  getPersonas: () => ipcRenderer.invoke('get-personas'),
  getPersona: (id) => ipcRenderer.invoke('get-persona', id),
  deletePersona: (id) => ipcRenderer.invoke('delete-persona', id),
  enhanceCharacterText: (data) => ipcRenderer.invoke('enhance-character-text', data),
  enhanceCharacterTextStream: (data) => ipcRenderer.invoke('enhance-character-text-stream', data),
  onEnhanceCharacterTextChunk: (callback) => ipcRenderer.on('enhance-character-text-chunk', callback),
  removeAllListeners: (channel) => ipcRenderer.removeAllListeners(channel),

  // Database viewer
  getAllTables: () => ipcRenderer.invoke('get-all-tables'),
  getTableSchema: (tableName) => ipcRenderer.invoke('get-table-schema', tableName),
  getTableData: (tableName, limit, offset) => ipcRenderer.invoke('get-table-data', tableName, limit, offset),
  deleteRow: (tableName, rowId, idColumn) => ipcRenderer.invoke('delete-row', tableName, rowId, idColumn),

  // Bootstrap helpers to set/get initial chat content and options
  setInitialChatBootstrap: (settingType, settingId, payload) => {
    try {
      const key = `${settingType}:${settingId}`;
      globalThis.__ELECTROBOX_BOOTSTRAP__.initialChatBySetting[key] = {
        content: payload?.content || '',
        options: Array.isArray(payload?.options) ? payload.options.slice(0, 4) : []
      };
      return true;
    } catch (e) {
      console.error('Failed to set bootstrap payload', e);
      return false;
    }
  },
  getInitialChatBootstrap: (settingType, settingId) => {
    try {
      const key = `${settingType}:${settingId}`;
      return globalThis.__ELECTROBOX_BOOTSTRAP__.initialChatBySetting[key] || null;
    } catch {
      return null;
    }
  },
});